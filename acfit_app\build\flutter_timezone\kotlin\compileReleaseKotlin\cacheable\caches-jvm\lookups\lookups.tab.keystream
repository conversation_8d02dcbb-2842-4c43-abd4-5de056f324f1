  Build 
android.os  VERSION android.os.Build  
VERSION_CODES android.os.Build  SDK_INT android.os.Build.VERSION  O android.os.Build.VERSION_CODES  NonNull androidx.annotation  
FlutterPlugin #io.flutter.embedding.engine.plugins  FlutterPluginBinding 1io.flutter.embedding.engine.plugins.FlutterPlugin  binaryMessenger Fio.flutter.embedding.engine.plugins.FlutterPlugin.FlutterPluginBinding  getBINARYMessenger Fio.flutter.embedding.engine.plugins.FlutterPlugin.FlutterPluginBinding  getBinaryMessenger Fio.flutter.embedding.engine.plugins.FlutterPlugin.FlutterPluginBinding  setBinaryMessenger Fio.flutter.embedding.engine.plugins.FlutterPlugin.FlutterPluginBinding  BinaryMessenger io.flutter.plugin.common  
MethodCall io.flutter.plugin.common  
MethodChannel io.flutter.plugin.common  method #io.flutter.plugin.common.MethodCall  MethodCallHandler &io.flutter.plugin.common.MethodChannel  Result &io.flutter.plugin.common.MethodChannel  setMethodCallHandler &io.flutter.plugin.common.MethodChannel  notImplemented -io.flutter.plugin.common.MethodChannel.Result  success -io.flutter.plugin.common.MethodChannel.Result  	ArrayList 	java.lang  Build 	java.lang  
MethodChannel 	java.lang  TimeZone 	java.lang  ZoneId 	java.lang  toCollection 	java.lang  ZoneId 	java.time  getAvailableZoneIds java.time.ZoneId  getID java.time.ZoneId  getId java.time.ZoneId  id java.time.ZoneId  setId java.time.ZoneId  
systemDefault java.time.ZoneId  	ArrayList 	java.util  Build 	java.util  
MethodChannel 	java.util  TimeZone 	java.util  ZoneId 	java.util  toCollection 	java.util  getAvailableIDs java.util.TimeZone  
getDefault java.util.TimeZone  getID java.util.TimeZone  getId java.util.TimeZone  id java.util.TimeZone  setID java.util.TimeZone  	ArrayList kotlin  Boolean kotlin  Build kotlin  Int kotlin  
MethodChannel kotlin  Nothing kotlin  String kotlin  TimeZone kotlin  ZoneId kotlin  toCollection kotlin  getTOCollection kotlin.Array  getToCollection kotlin.Array  	ArrayList kotlin.annotation  Build kotlin.annotation  
MethodChannel kotlin.annotation  TimeZone kotlin.annotation  ZoneId kotlin.annotation  toCollection kotlin.annotation  	ArrayList kotlin.collections  Build kotlin.collections  List kotlin.collections  
MethodChannel kotlin.collections  TimeZone kotlin.collections  ZoneId kotlin.collections  toCollection kotlin.collections  getTOCollection kotlin.collections.MutableSet  getToCollection kotlin.collections.MutableSet  	ArrayList kotlin.comparisons  Build kotlin.comparisons  
MethodChannel kotlin.comparisons  TimeZone kotlin.comparisons  ZoneId kotlin.comparisons  toCollection kotlin.comparisons  	ArrayList 	kotlin.io  Build 	kotlin.io  
MethodChannel 	kotlin.io  TimeZone 	kotlin.io  ZoneId 	kotlin.io  toCollection 	kotlin.io  	ArrayList 
kotlin.jvm  Build 
kotlin.jvm  
MethodChannel 
kotlin.jvm  TimeZone 
kotlin.jvm  ZoneId 
kotlin.jvm  toCollection 
kotlin.jvm  	ArrayList 
kotlin.ranges  Build 
kotlin.ranges  
MethodChannel 
kotlin.ranges  TimeZone 
kotlin.ranges  ZoneId 
kotlin.ranges  toCollection 
kotlin.ranges  	ArrayList kotlin.sequences  Build kotlin.sequences  
MethodChannel kotlin.sequences  TimeZone kotlin.sequences  ZoneId kotlin.sequences  toCollection kotlin.sequences  	ArrayList kotlin.text  Build kotlin.text  
MethodChannel kotlin.text  TimeZone kotlin.text  ZoneId kotlin.text  toCollection kotlin.text  	ArrayList #net.wolverinebeach.flutter_timezone  Build #net.wolverinebeach.flutter_timezone  FlutterTimezonePlugin #net.wolverinebeach.flutter_timezone  List #net.wolverinebeach.flutter_timezone  
MethodChannel #net.wolverinebeach.flutter_timezone  String #net.wolverinebeach.flutter_timezone  TimeZone #net.wolverinebeach.flutter_timezone  ZoneId #net.wolverinebeach.flutter_timezone  toCollection #net.wolverinebeach.flutter_timezone  	ArrayList 9net.wolverinebeach.flutter_timezone.FlutterTimezonePlugin  Build 9net.wolverinebeach.flutter_timezone.FlutterTimezonePlugin  
FlutterPlugin 9net.wolverinebeach.flutter_timezone.FlutterTimezonePlugin  List 9net.wolverinebeach.flutter_timezone.FlutterTimezonePlugin  
MethodCall 9net.wolverinebeach.flutter_timezone.FlutterTimezonePlugin  
MethodChannel 9net.wolverinebeach.flutter_timezone.FlutterTimezonePlugin  NonNull 9net.wolverinebeach.flutter_timezone.FlutterTimezonePlugin  Result 9net.wolverinebeach.flutter_timezone.FlutterTimezonePlugin  String 9net.wolverinebeach.flutter_timezone.FlutterTimezonePlugin  TimeZone 9net.wolverinebeach.flutter_timezone.FlutterTimezonePlugin  ZoneId 9net.wolverinebeach.flutter_timezone.FlutterTimezonePlugin  channel 9net.wolverinebeach.flutter_timezone.FlutterTimezonePlugin  getAvailableTimezones 9net.wolverinebeach.flutter_timezone.FlutterTimezonePlugin  getLocalTimezone 9net.wolverinebeach.flutter_timezone.FlutterTimezonePlugin  getTOCollection 9net.wolverinebeach.flutter_timezone.FlutterTimezonePlugin  getToCollection 9net.wolverinebeach.flutter_timezone.FlutterTimezonePlugin  toCollection 9net.wolverinebeach.flutter_timezone.FlutterTimezonePlugin                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                        