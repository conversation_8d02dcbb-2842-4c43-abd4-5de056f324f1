# AC-FIT Backend Deployment Guide - Google Cloud Compute Engine

This is a comprehensive guide to deploy the AC-FIT Django backend on Google Cloud Compute Engine using nginx and gunicorn (NO DOCKER). This guide includes all the common mistakes and how to avoid them.

## 🚨 IMPORTANT NOTES BEFORE STARTING

- **NO DOCKER**: This guide uses direct deployment with nginx + gunicorn
- **Single Instance**: Everything runs on one compute instance (PostgreSQL, Django, nginx)
- **HTTP Only**: This setup uses HTTP (not HTTPS) for simplicity
- **Root Access**: You'll need sudo access on the compute instance

---

## Phase 1: Google Cloud Setup

### Step 1: Create Compute Instance

```bash
# Login to Google Cloud (run this on your LOCAL machine)
gcloud auth login

# Set your project (replace 'your-project-id' with your actual project ID)
gcloud config set project your-project-id

# Create a compute instance
gcloud compute instances create acfit-backend-server \
    --zone=us-central1-a \
    --machine-type=e2-medium \
    --network-tier=PREMIUM \
    --maintenance-policy=MIGRATE \
    --image-family=ubuntu-2004-lts \
    --image-project=ubuntu-os-cloud \
    --boot-disk-size=20GB \
    --boot-disk-type=pd-standard \
    --tags=http-server,https-server

# Allow HTTP and HTTPS traffic
gcloud compute firewall-rules create allow-http-https \
    --allow tcp:80,tcp:443,tcp:8000 \
    --source-ranges 0.0.0.0/0 \
    --target-tags http-server,https-server
```

### Step 2: Connect to Your Instance

```bash
# SSH into your instance (run this on your LOCAL machine)
gcloud compute ssh acfit-backend-server --zone=us-central1-a
```

**🚨 IMPORTANT**: All commands from Step 3 onwards should be run ON THE SERVER (after SSH)

---

## Phase 2: Server Setup (Run ALL commands below ON THE SERVER)

### Step 3: Update System and Install Dependencies

```bash
# Update system
sudo apt update && sudo apt upgrade -y

# Install Python, PostgreSQL, nginx, and other dependencies
sudo apt install -y python3 python3-pip python3-venv postgresql postgresql-contrib nginx git curl

# Install additional system dependencies for Python packages
sudo apt install -y build-essential libpq-dev python3-dev python3-full
```

### Step 4: Setup PostgreSQL Database

```bash
# Switch to postgres user and create database
sudo -u postgres psql

# In PostgreSQL shell, run these commands ONE BY ONE:
CREATE USER daksh WITH PASSWORD 'daksh';
CREATE DATABASE acfit OWNER daksh;
GRANT ALL PRIVILEGES ON DATABASE acfit TO daksh;
ALTER USER daksh CREATEDB;
ALTER USER daksh SUPERUSER;

# Exit PostgreSQL (type this in the PostgreSQL shell)
\q
```

**🚨 COMMON MISTAKE**: Make sure you type `\q` to exit PostgreSQL before continuing!

### Step 5: Test Database Connection

```bash
# Test if you can connect to the database
psql -h localhost -U daksh -d acfit

# If it asks for password, enter: daksh
# If it connects successfully, type \q to exit
\q
```

---

## Phase 3: Upload and Setup Your Code

### Step 6: Upload Your Backend Code

**On your LOCAL machine** (open a new terminal, don't close the SSH session):

```bash
# Navigate to your AC-FIT project directory
cd /path/to/your/AC-FIT

# Create a compressed file of your backend (excluding unnecessary files)
tar -czf backend.tar.gz backend/ --exclude=backend/__pycache__ --exclude=backend/media --exclude=backend/staticfiles --exclude=backend/.env.local --exclude=backend/venv

# Copy to your server (replace 'acfit-backend-server' with your instance name)
gcloud compute scp backend.tar.gz acfit-backend-server:/tmp/ --zone=us-central1-a
```

### Step 7: Extract Code on Server

**Back on the SERVER** (in your SSH session):

```bash
# Create directory for your application
mkdir -p ~/backendf
cd ~/backendf

# Extract the uploaded code
tar -xzf /tmp/backend.tar.gz --strip-components=1

# Verify the code is there
ls -la
# You should see manage.py, requirements.txt, etc.
```

**🚨 COMMON MISTAKE**: Make sure you're in the `~/backendf` directory for all subsequent commands!

### Step 8: Setup Python Virtual Environment

```bash
# Make sure you're in the project directory
cd ~/backendf

# Create virtual environment
python3 -m venv venv

# Activate virtual environment
source venv/bin/activate

# You should see (venv) in your prompt now

# Upgrade pip
python -m pip install --upgrade pip

# Install all requirements
python -m pip install -r requirements.txt

# Verify gunicorn is installed
which gunicorn
# Should show: /home/<USER>/backendf/venv/bin/gunicorn
```

**🚨 COMMON MISTAKE**: If you get "externally-managed-environment" error, make sure you installed `python3-full` in Step 3!

### Step 9: Create Production Environment File

```bash
# Make sure you're in ~/backendf and venv is activated
cd ~/backendf

# Get your server's external IP
EXTERNAL_IP=$(curl -H "Metadata-Flavor: Google" http://metadata.google.internal/computeMetadata/v1/instance/network-interfaces/0/access-configs/0/external-ip)
echo "Your server IP is: $EXTERNAL_IP"

# Create .env file (replace the IP in the command below with your actual IP)
cat > .env << 'EOF'
DEBUG=False
ENVIRONMENT=production
SECRET_KEY=django-insecure-change-this-to-a-very-long-random-string-in-production
ALLOWED_HOSTS=YOUR_SERVER_IP;localhost
CSRF_TRUSTED_ORIGINS=http://YOUR_SERVER_IP;https://YOUR_SERVER_IP

# Database settings - Local PostgreSQL
DB_NAME=acfit
DB_USER=daksh
DB_PASSWORD=daksh
DB_HOST=localhost
DB_PORT=5432

# SMTP Settings
EMAIL_BACKEND=django.core.mail.backends.smtp.EmailBackend
EMAIL_HOST=smtp.hostinger.com
EMAIL_PORT=465
EMAIL_USE_TLS=False
EMAIL_USE_SSL=True
EMAIL_HOST_USER=<EMAIL>
EMAIL_HOST_PASSWORD=ACFit@2025
DEFAULT_FROM_EMAIL=<EMAIL>

# Frontend URL for password reset links
FRONTEND_URL=http://YOUR_SERVER_IP

# CORS settings
CORS_ALLOWED_ORIGINS=http://YOUR_SERVER_IP

# API Keys
YOUTUBE_API_KEY=your-youtube-api-key
GIPHY_API_KEY=AP703A14Crc2Dwv1zrF022Q2iLatsCZM
UNSPLASH_ACCESS_KEY=*******************************************
UNSPLASH_SECRET_KEY=*******************************************
EOF

# Replace YOUR_SERVER_IP with your actual IP in the .env file
sed -i "s/YOUR_SERVER_IP/$EXTERNAL_IP/g" .env

# Verify the .env file looks correct
cat .env
```

**🚨 CRITICAL**: Make sure the .env file shows your actual IP address, not "YOUR_SERVER_IP"!

### Step 10: Fix Django Settings for HTTP Access

**🚨 IMPORTANT**: This step fixes CSRF issues that occur when using HTTP instead of HTTPS.

```bash
# Edit Django settings to allow HTTP access
nano acfit_backend/settings.py
```

Find the section around line 168 that looks like this:
```python
else:
    # Security settings for production
    SECURE_SSL_REDIRECT = False
    SECURE_PROXY_SSL_HEADER = ('HTTP_X_FORWARDED_PROTO', 'https')
    SESSION_COOKIE_SECURE = True
    CSRF_COOKIE_SECURE = True
```

**Change it to:**
```python
else:
    # Security settings for production
    SECURE_SSL_REDIRECT = False
    SECURE_PROXY_SSL_HEADER = ('HTTP_X_FORWARDED_PROTO', 'https')
    SESSION_COOKIE_SECURE = False  # Changed to False for HTTP access
    CSRF_COOKIE_SECURE = False     # Changed to False for HTTP access
    CSRF_COOKIE_HTTPONLY = False   # Allow JavaScript access for debugging
    CSRF_COOKIE_SAMESITE = None    # More permissive for debugging
    SECURE_BROWSER_XSS_FILTER = True
    SECURE_CONTENT_TYPE_NOSNIFF = True
    X_FRAME_OPTIONS = 'DENY'
    # Disable HSTS for HTTP access
    SECURE_HSTS_SECONDS = 0
    SECURE_HSTS_INCLUDE_SUBDOMAINS = False
    SECURE_HSTS_PRELOAD = False
```

**🚨 CRITICAL**: This step is essential to avoid CSRF verification errors!

### Step 11: Run Django Setup

```bash
# Make sure you're in ~/backendf and venv is activated
cd ~/backendf
source venv/bin/activate

# Test database connection
python manage.py dbshell
# If it connects, type \q to exit

# Run migrations
python manage.py migrate

# Create superuser
python manage.py createsuperuser

# Collect static files
python manage.py collectstatic --noinput

# Populate database with initial data
python manage.py populate_all_data

# Test that Django works
python manage.py runserver 0.0.0.0:8000
# Test by visiting http://YOUR_SERVER_IP:8000 in browser
# Press Ctrl+C to stop the test server
```

---

## Phase 4: Setup Gunicorn

### Step 12: Create Gunicorn Configuration

```bash
# Make sure you're in ~/backendf
cd ~/backendf

# Create logs directory
mkdir -p logs

# Create gunicorn configuration
cat > gunicorn.conf.py << 'EOF'
bind = "127.0.0.1:8000"
workers = 2
worker_class = "sync"
worker_connections = 1000
timeout = 30
keepalive = 2
max_requests = 1000
max_requests_jitter = 100
preload_app = True
daemon = False
tmp_upload_dir = None
errorlog = "/home/<USER>/backendf/logs/error.log"
accesslog = "/home/<USER>/backendf/logs/access.log"
loglevel = "info"
EOF

# Replace YOUR_USERNAME with your actual username
sed -i "s/YOUR_USERNAME/$USER/g" gunicorn.conf.py
```

### Step 13: Test Gunicorn Manually

```bash
# Test gunicorn manually first
cd ~/backendf
source venv/bin/activate
gunicorn --config gunicorn.conf.py acfit_backend.wsgi:application

# If this works (you should see gunicorn starting), press Ctrl+C to stop it
```

**🚨 COMMON MISTAKE**: If you get permission errors, make sure the logs directory exists and has correct permissions!

### Step 14: Create Systemd Service for Gunicorn

```bash
# Create systemd service file
sudo nano /etc/systemd/system/gunicorn.service
```

**Add this content** (replace YOUR_USERNAME with your actual username):

```ini
[Unit]
Description=gunicorn daemon for acfit backend
After=network.target

[Service]
Type=simple
User=YOUR_USERNAME
Group=YOUR_USERNAME
WorkingDirectory=/home/<USER>/backendf
Environment=PATH=/home/<USER>/backendf/venv/bin
ExecStart=/home/<USER>/backendf/venv/bin/gunicorn --config gunicorn.conf.py acfit_backend.wsgi:application
Restart=on-failure
RestartSec=5

[Install]
WantedBy=multi-user.target
```

**🚨 IMPORTANT**: Replace `YOUR_USERNAME` with your actual username in the service file!

### Step 15: Start Gunicorn Service

```bash
# Reload systemd
sudo systemctl daemon-reload

# Start gunicorn
sudo systemctl start gunicorn

# Check status
sudo systemctl status gunicorn

# Enable for auto-start
sudo systemctl enable gunicorn

# Check if gunicorn is listening on port 8000
sudo netstat -tlnp | grep :8000
```

**🚨 TROUBLESHOOTING**: If gunicorn fails to start, check logs with:
```bash
sudo journalctl -u gunicorn -n 20
```

---

## Phase 5: Setup Nginx

### Step 16: Create Nginx Configuration

```bash
# Remove default nginx site
sudo rm /etc/nginx/sites-enabled/default

# Create new site configuration
sudo nano /etc/nginx/sites-available/acfit
```

**Add this content** (replace YOUR_SERVER_IP with your actual IP):

```nginx
server {
    listen 80;
    server_name YOUR_SERVER_IP your-domain.com;

    client_max_body_size 100M;

    # Static files
    location /static/ {
        alias /home/<USER>/backendf/staticfiles/;
        expires 30d;
        add_header Cache-Control "public, immutable";
    }

    # Media files
    location /media/ {
        alias /home/<USER>/backendf/media/;
        expires 30d;
        add_header Cache-Control "public";
    }

    # Main application
    location / {
        proxy_pass http://127.0.0.1:8000;
        proxy_set_header Host $host;
        proxy_set_header X-Real-IP $remote_addr;
        proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
        proxy_set_header X-Forwarded-Proto $scheme;
        proxy_connect_timeout 300s;
        proxy_send_timeout 300s;
        proxy_read_timeout 300s;
    }

    # Security headers
    add_header X-Frame-Options "SAMEORIGIN" always;
    add_header X-Content-Type-Options "nosniff" always;
    add_header X-XSS-Protection "1; mode=block" always;
    add_header Referrer-Policy "no-referrer-when-downgrade" always;
}
```

### Step 17: Update Nginx Configuration with Your Details

```bash
# Get your external IP and username
EXTERNAL_IP=$(curl -H "Metadata-Flavor: Google" http://metadata.google.internal/computeMetadata/v1/instance/network-interfaces/0/access-configs/0/external-ip)
USERNAME=$(whoami)

# Update the nginx configuration with actual values
sudo sed -i "s/YOUR_SERVER_IP/$EXTERNAL_IP/g" /etc/nginx/sites-available/acfit
sudo sed -i "s/YOUR_USERNAME/$USERNAME/g" /etc/nginx/sites-available/acfit
```

### Step 18: Enable Nginx Site and Start Service

```bash
# Enable the site
sudo ln -s /etc/nginx/sites-available/acfit /etc/nginx/sites-enabled/

# Test nginx configuration
sudo nginx -t

# Start and enable nginx
sudo systemctl start nginx
sudo systemctl enable nginx

# Check status
sudo systemctl status nginx
```

---

## Phase 6: Final Testing and Verification

### Step 19: Restart All Services

```bash
# Restart all services to ensure everything is working
sudo systemctl restart postgresql gunicorn nginx

# Check status of all services
sudo systemctl status postgresql gunicorn nginx
```

### Step 20: Test Your Deployment

```bash
# Get your server IP
EXTERNAL_IP=$(curl -H "Metadata-Flavor: Google" http://metadata.google.internal/computeMetadata/v1/instance/network-interfaces/0/access-configs/0/external-ip)
echo "Your backend is available at: http://$EXTERNAL_IP"

# Test locally on the server
curl http://localhost/admin/
curl http://$EXTERNAL_IP/admin/

# Check if all services are listening on correct ports
sudo netstat -tlnp | grep -E ':80|:8000|:5432'
```

**🎉 SUCCESS**: Your backend should now be accessible at:
- **Admin Panel**: `http://YOUR_SERVER_IP/admin/`
- **API Root**: `http://YOUR_SERVER_IP/api/`
- **Health Check**: `http://YOUR_SERVER_IP/api/accounts/health/`

---

## Phase 7: Enable HTTPS (Optional but Recommended)

### Step 21: Generate Self-Signed SSL Certificate

```bash
# Create SSL directory
sudo mkdir -p /etc/ssl/private /etc/ssl/certs

# Get your server IP
EXTERNAL_IP=$(curl -H "Metadata-Flavor: Google" http://metadata.google.internal/computeMetadata/v1/instance/network-interfaces/0/access-configs/0/external-ip)

# Generate self-signed certificate for your IP address
sudo openssl req -x509 -nodes -days 365 -newkey rsa:2048 \
    -keyout /etc/ssl/private/acfit.key \
    -out /etc/ssl/certs/acfit.crt \
    -subj "/C=US/ST=State/L=City/O=Organization/CN=$EXTERNAL_IP"

# Set proper permissions
sudo chmod 600 /etc/ssl/private/acfit.key
sudo chmod 644 /etc/ssl/certs/acfit.crt
```

### Step 22: Update Nginx Configuration for HTTPS

```bash
# Get your username and IP
USERNAME=$(whoami)
EXTERNAL_IP=$(curl -H "Metadata-Flavor: Google" http://metadata.google.internal/computeMetadata/v1/instance/network-interfaces/0/access-configs/0/external-ip)

# Backup current nginx config
sudo cp /etc/nginx/sites-available/acfit /etc/nginx/sites-available/acfit.backup

# Create new HTTPS nginx configuration
sudo tee /etc/nginx/sites-available/acfit > /dev/null << EOF
# Redirect HTTP to HTTPS
server {
    listen 80;
    server_name $EXTERNAL_IP;
    return 301 https://\$server_name\$request_uri;
}

# HTTPS server
server {
    listen 443 ssl;
    server_name $EXTERNAL_IP;

    # SSL Configuration
    ssl_certificate /etc/ssl/certs/acfit.crt;
    ssl_certificate_key /etc/ssl/private/acfit.key;

    # SSL Settings
    ssl_protocols TLSv1.2 TLSv1.3;
    ssl_ciphers HIGH:!aNULL:!MD5;
    ssl_prefer_server_ciphers on;

    client_max_body_size 100M;

    # Static files
    location /static/ {
        alias /home/<USER>/backendf/staticfiles/;
        expires 30d;
        add_header Cache-Control "public, immutable";
    }

    # Media files
    location /media/ {
        alias /home/<USER>/backendf/media/;
        expires 30d;
        add_header Cache-Control "public";
    }

    # Main application
    location / {
        proxy_pass http://127.0.0.1:8000;
        proxy_set_header Host \$host;
        proxy_set_header X-Real-IP \$remote_addr;
        proxy_set_header X-Forwarded-For \$proxy_add_x_forwarded_for;
        proxy_set_header X-Forwarded-Proto \$scheme;
        proxy_connect_timeout 300s;
        proxy_send_timeout 300s;
        proxy_read_timeout 300s;
    }

    # Security headers
    add_header X-Frame-Options "SAMEORIGIN" always;
    add_header X-Content-Type-Options "nosniff" always;
    add_header X-XSS-Protection "1; mode=block" always;
    add_header Referrer-Policy "no-referrer-when-downgrade" always;
}
EOF
```

### Step 23: Update Django Settings for HTTPS

```bash
# Update .env file for HTTPS
cd ~/backendf
EXTERNAL_IP=$(curl -H "Metadata-Flavor: Google" http://metadata.google.internal/computeMetadata/v1/instance/network-interfaces/0/access-configs/0/external-ip)

# Update .env file
sed -i "s|ALLOWED_HOSTS=.*|ALLOWED_HOSTS=$EXTERNAL_IP;localhost|g" .env
sed -i "s|CSRF_TRUSTED_ORIGINS=.*|CSRF_TRUSTED_ORIGINS=https://$EXTERNAL_IP;http://localhost:3000;http://127.0.0.1:3000|g" .env
sed -i "s|FRONTEND_URL=.*|FRONTEND_URL=https://$EXTERNAL_IP|g" .env
sed -i "s|CORS_ALLOWED_ORIGINS=.*|CORS_ALLOWED_ORIGINS=https://$EXTERNAL_IP;http://localhost:3000;http://127.0.0.1:3000;http://localhost:8080;http://127.0.0.1:8080|g" .env

# Verify changes
cat .env | grep -E "ALLOWED_HOSTS|CSRF_TRUSTED_ORIGINS|FRONTEND_URL|CORS_ALLOWED_ORIGINS"
```

### Step 24: Enable HTTPS Firewall Rule

```bash
# Allow HTTPS traffic (run this on your LOCAL machine)
gcloud compute firewall-rules create allow-https \
    --allow tcp:443 \
    --source-ranges 0.0.0.0/0 \
    --target-tags https-server

# Add https-server tag to your instance
gcloud compute instances add-tags acfit-backend-server \
    --tags https-server \
    --zone us-central1-a
```

### Step 25: Test and Restart Services

```bash
# Test nginx configuration
sudo nginx -t

# Restart services
sudo systemctl restart nginx gunicorn

# Check status
sudo systemctl status nginx gunicorn

# Test HTTPS (ignore certificate warnings with -k)
curl -k https://YOUR_SERVER_IP/admin/
```

**🎉 HTTPS SUCCESS**: Your backend is now accessible at:
- **HTTPS Admin Panel**: `https://YOUR_SERVER_IP/admin/`
- **HTTPS API Root**: `https://YOUR_SERVER_IP/api/`
- **HTTP redirects to HTTPS**: `http://YOUR_SERVER_IP/` → `https://YOUR_SERVER_IP/`

**⚠️ Certificate Warning**: Browsers will show security warnings for self-signed certificates. Click "Advanced" → "Proceed" to access the site.

---

## 🚨 Common Issues and Solutions

### Issue 1: CSRF Verification Failed
**Symptoms**: Getting "CSRF verification failed" when accessing admin panel
**Solution**: Make sure you completed Step 10 (Fix Django Settings for HTTP Access)

### Issue 2: Gunicorn Won't Start
**Symptoms**: `sudo systemctl status gunicorn` shows failed status
**Solutions**:
```bash
# Check logs
sudo journalctl -u gunicorn -n 20

# Common fixes:
# 1. Wrong paths in systemd service file
# 2. Virtual environment not in correct location
# 3. Permission issues with log files

# Test gunicorn manually first:
cd ~/backendf
source venv/bin/activate
gunicorn --config gunicorn.conf.py acfit_backend.wsgi:application
```

### Issue 3: 502 Bad Gateway
**Symptoms**: Nginx shows 502 error
**Solutions**:
```bash
# Check if gunicorn is running
sudo systemctl status gunicorn
sudo netstat -tlnp | grep :8000

# Check nginx error logs
sudo tail -f /var/log/nginx/error.log

# Restart services
sudo systemctl restart gunicorn nginx
```

### Issue 4: Database Connection Error
**Symptoms**: "connection to server failed" errors
**Solutions**:
```bash
# Test database connection
psql -h localhost -U daksh -d acfit

# Check PostgreSQL status
sudo systemctl status postgresql

# Recreate database user if needed
sudo -u postgres psql
DROP USER IF EXISTS daksh;
CREATE USER daksh WITH PASSWORD 'daksh';
CREATE DATABASE acfit OWNER daksh;
GRANT ALL PRIVILEGES ON DATABASE acfit TO daksh;
ALTER USER daksh CREATEDB;
ALTER USER daksh SUPERUSER;
\q
```

---

## 🔄 Running Server in Background

### Using Screen (Recommended for Development)

```bash
# Install screen if not already installed
sudo apt install screen -y

# Start a new screen session
screen -S acfit-server

# Inside screen, go to your project directory
cd ~/backendf
source venv/bin/activate

# Run your server (optional - services should already be running)
python manage.py runserver 0.0.0.0:8000

# Detach from screen (server keeps running)
# Press: Ctrl+A, then D

# To reattach to the screen session later
screen -r acfit-server

# To list all screen sessions
screen -ls

# To kill a screen session
screen -S acfit-server -X quit
```

### Using Systemd Services (Production - Already Configured)

Your server is already configured to run in background using systemd services:

```bash
# Check if services are running in background
sudo systemctl status postgresql gunicorn nginx

# Services will automatically start on server reboot
sudo systemctl is-enabled postgresql gunicorn nginx

# To stop services
sudo systemctl stop gunicorn nginx

# To start services
sudo systemctl start gunicorn nginx

# To restart services
sudo systemctl restart gunicorn nginx
```

### Using nohup (Alternative)

```bash
# Run Django in background with nohup
cd ~/backendf
source venv/bin/activate
nohup python manage.py runserver 0.0.0.0:8000 > server.log 2>&1 &

# Check if it's running
ps aux | grep python

# To stop it, find the process ID and kill it
kill <process_id>
```

**🎯 Recommended**: Use the systemd services (gunicorn + nginx) as they're already configured and will auto-restart on failure.

---

## 📋 Management Commands

### Restart All Services
```bash
sudo systemctl restart postgresql gunicorn nginx
```

### View Logs
```bash
# Gunicorn logs
sudo journalctl -u gunicorn -f

# Nginx logs
sudo tail -f /var/log/nginx/access.log
sudo tail -f /var/log/nginx/error.log

# Application logs
tail -f ~/backendf/logs/error.log
tail -f ~/backendf/logs/access.log
```

### Update Your Application
```bash
# 1. Upload new code from local machine
gcloud compute scp backend.tar.gz acfit-backend-server:/tmp/ --zone=us-central1-a

# 2. On server, extract and update
cd ~/backendf
tar -xzf /tmp/backend.tar.gz --strip-components=1
source venv/bin/activate
pip install -r requirements.txt
python manage.py migrate
python manage.py collectstatic --noinput

# 3. Restart gunicorn
sudo systemctl restart gunicorn
```

### Database Backup
```bash
# Create backup
sudo -u postgres pg_dump acfit > /tmp/acfit_backup_$(date +%Y%m%d_%H%M%S).sql

# Restore from backup
sudo -u postgres psql acfit < /tmp/acfit_backup_YYYYMMDD_HHMMSS.sql
```

---

## 💰 Estimated Monthly Costs

- **e2-medium instance**: ~$25-35/month
- **20GB persistent disk**: ~$2/month
- **Network egress**: ~$1-5/month (depending on traffic)
- **Total**: ~$30-45/month

---

## 🔒 Security Notes

**⚠️ IMPORTANT**: This setup is configured for HTTP access for simplicity. For production use, consider:

1. **Enable HTTPS** with Let's Encrypt
2. **Change default passwords** for database and Django secret key
3. **Set up firewall rules** to restrict access
4. **Enable automatic security updates**
5. **Set up monitoring and alerting**

---

## 📞 Support

If you encounter issues not covered in this guide:

1. **Check the logs** first (see Management Commands section)
2. **Verify all services are running**: `sudo systemctl status postgresql gunicorn nginx`
3. **Test each component individually** (database, Django, gunicorn, nginx)
4. **Check firewall rules** and network connectivity

---

**🎉 Congratulations!** Your AC-FIT backend is now deployed and running on Google Cloud!

