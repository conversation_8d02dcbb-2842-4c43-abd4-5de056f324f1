<lint-module
    format="1"
    dir="C:\Users\<USER>\AppData\Local\Pub\Cache\hosted\pub.dev\audioplayers_android-4.0.3\android"
    name=":audioplayers_android"
    type="LIBRARY"
    maven="xyz.luan.audioplayers:audioplayers_android:1.0-SNAPSHOT"
    agpVersion="8.7.0"
    buildFolder="D:\PROGRAMMING\AC-FIT\acfit_app\build\audioplayers_android"
    bootClassPath="C:\Users\<USER>\AppData\Local\Android\sdk\platforms\android-33\android.jar;C:\Users\<USER>\AppData\Local\Android\sdk\build-tools\34.0.0\core-lambda-stubs.jar"
    javaSourceLevel="1.8"
    compileTarget="android-33"
    neverShrinking="true">
  <lintOptions
      disable="InvalidPackage"
      abortOnError="true"
      absolutePaths="true"
      checkReleaseBuilds="true"
      explainIssues="true">
    <severities>
      <severity
        id="InvalidPackage"
        severity="IGNORE" />
    </severities>
  </lintOptions>
  <variant name="release"/>
</lint-module>
