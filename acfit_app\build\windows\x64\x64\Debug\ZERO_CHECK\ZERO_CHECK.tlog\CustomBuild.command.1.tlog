^D:\PROGRAMMING\AC-FIT\ACFIT_APP\BUILD\WINDOWS\X64\CMAKEFILES\E31F40AC03A4244013D5BCD93CD9EC41\GENERATE.STAMP.RULE
setlocal
"C:\Program Files\Microsoft Visual Studio\2022\Community\Common7\IDE\CommonExtensions\Microsoft\CMake\CMake\bin\cmake.exe" -SD:/PROGRAMMING/AC-FIT/acfit_app/windows -BD:/PROGRAMMING/AC-FIT/acfit_app/build/windows/x64 --check-stamp-list CMakeFiles/generate.stamp.list --vs-solution-file D:/PROGRAMMING/AC-FIT/acfit_app/build/windows/x64/acfit_app.sln
if %errorlevel% neq 0 goto :cmEnd
:cmEnd
endlocal & call :cmErrorLevel %errorlevel% & goto :cmDone
:cmErrorLevel
exit /b %1
:cmDone
if %errorlevel% neq 0 goto :VCEnd
