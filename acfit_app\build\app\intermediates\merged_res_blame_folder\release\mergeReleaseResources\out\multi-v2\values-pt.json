{"logs": [{"outputFile": "com.example.acfit_app-mergeReleaseResources-49:/values-pt/values-pt.xml", "map": [{"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.10.2\\transforms\\deb838f5c4813d0c441214e1ddf497cc\\transformed\\core-1.13.1\\res\\values-pt\\values-pt.xml", "from": {"startLines": "2,3,4,5,6,7,8,9", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "55,152,254,353,453,560,670,790", "endColumns": "96,101,98,99,106,109,119,100", "endOffsets": "147,249,348,448,555,665,785,886"}, "to": {"startLines": "29,30,31,32,33,34,35,54", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "2843,2940,3042,3141,3241,3348,3458,5168", "endColumns": "96,101,98,99,106,109,119,100", "endOffsets": "2935,3037,3136,3236,3343,3453,3573,5264"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.10.2\\transforms\\f3900c31e436e087be46264d65f1dea9\\transformed\\jetified-media3-exoplayer-1.4.1\\res\\values-pt\\values-pt.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10", "startColumns": "4,4,4,4,4,4,4,4,4", "startOffsets": "55,125,195,267,333,410,477,578,671", "endColumns": "69,69,71,65,76,66,100,92,69", "endOffsets": "120,190,262,328,405,472,573,666,736"}, "to": {"startLines": "38,39,40,41,42,43,44,45,46", "startColumns": "4,4,4,4,4,4,4,4,4", "startOffsets": "3763,3833,3903,3975,4041,4118,4185,4286,4379", "endColumns": "69,69,71,65,76,66,100,92,69", "endOffsets": "3828,3898,3970,4036,4113,4180,4281,4374,4444"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.10.2\\transforms\\5683f3a75f4aae74864d56d25d09ab49\\transformed\\browser-1.8.0\\res\\values-pt\\values-pt.xml", "from": {"startLines": "2,3,4,5", "startColumns": "4,4,4,4", "startOffsets": "55,170,269,381", "endColumns": "114,98,111,105", "endOffsets": "165,264,376,482"}, "to": {"startLines": "37,48,49,50", "startColumns": "4,4,4,4", "startOffsets": "3648,4536,4635,4747", "endColumns": "114,98,111,105", "endOffsets": "3758,4630,4742,4848"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.10.2\\transforms\\32d7d149835c8c00f0697dfcab9adb84\\transformed\\appcompat-1.1.0\\res\\values-pt\\values-pt.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "105,225,331,438,527,628,747,832,913,1004,1096,1191,1285,1386,1479,1574,1669,1760,1851,1935,2042,2153,2255,2363,2471,2581,2743,2843", "endColumns": "119,105,106,88,100,118,84,80,90,91,94,93,100,92,94,94,90,90,83,106,110,101,107,107,109,161,99,84", "endOffsets": "220,326,433,522,623,742,827,908,999,1091,1186,1280,1381,1474,1569,1664,1755,1846,1930,2037,2148,2250,2358,2466,2576,2738,2838,2923"}, "to": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,53", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "105,225,331,438,527,628,747,832,913,1004,1096,1191,1285,1386,1479,1574,1669,1760,1851,1935,2042,2153,2255,2363,2471,2581,2743,5083", "endColumns": "119,105,106,88,100,118,84,80,90,91,94,93,100,92,94,94,90,90,83,106,110,101,107,107,109,161,99,84", "endOffsets": "220,326,433,522,623,742,827,908,999,1091,1186,1280,1381,1474,1569,1664,1755,1846,1930,2037,2148,2250,2358,2466,2576,2738,2838,5163"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.10.2\\transforms\\0aea3dee6044b352a54f8fb9c4a12c07\\transformed\\preference-1.2.1\\res\\values-pt\\values-pt.xml", "from": {"startLines": "2,3,4,5,6,7,8", "startColumns": "4,4,4,4,4,4,4", "startOffsets": "105,175,262,341,492,661,748", "endColumns": "69,86,78,150,168,86,80", "endOffsets": "170,257,336,487,656,743,824"}, "to": {"startLines": "36,47,51,52,55,56,57", "startColumns": "4,4,4,4,4,4,4", "startOffsets": "3578,4449,4853,4932,5269,5438,5525", "endColumns": "69,86,78,150,168,86,80", "endOffsets": "3643,4531,4927,5078,5433,5520,5601"}}]}]}