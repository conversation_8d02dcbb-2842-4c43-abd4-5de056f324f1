<lint-module
    format="1"
    dir="C:\Users\<USER>\AppData\Local\Pub\Cache\hosted\pub.dev\connectivity_plus-5.0.2\android"
    name=":connectivity_plus"
    type="LIBRARY"
    maven="dev.fluttercommunity.plus.connectivity:connectivity_plus:1.0-SNAPSHOT"
    agpVersion="8.7.0"
    buildFolder="D:\PROGRAMMING\AC-FIT\acfit_app\build\connectivity_plus"
    bootClassPath="C:\Users\<USER>\AppData\Local\Android\sdk\platforms\android-33\android.jar;C:\Users\<USER>\AppData\Local\Android\sdk\build-tools\34.0.0\core-lambda-stubs.jar"
    javaSourceLevel="1.8"
    compileTarget="android-33"
    neverShrinking="true">
  <lintOptions
      disable="InvalidPackage"
      abortOnError="true"
      absolutePaths="true"
      checkReleaseBuilds="true"
      explainIssues="true">
    <severities>
      <severity
        id="InvalidPackage"
        severity="IGNORE" />
    </severities>
  </lintOptions>
  <variant name="release"/>
</lint-module>
