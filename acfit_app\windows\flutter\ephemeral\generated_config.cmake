# Generated code do not commit.
file(TO_CMAKE_PATH "E:\\utils\\flutter" FLUTTER_ROOT)
file(TO_CMAKE_PATH "D:\\PROGRAMMING\\AC-FIT\\acfit_app" PROJECT_DIR)

set(FLUTTER_VERSION "0.1.0" PARENT_SCOPE)
set(FLUTTER_VERSION_MAJOR 0 PARENT_SCOPE)
set(FLUTTER_VERSION_MINOR 1 PARENT_SCOPE)
set(FLUTTER_VERSION_PATCH 0 PARENT_SCOPE)
set(FLUTTER_VERSION_BUILD 0 PARENT_SCOPE)

# Environment variables to pass to tool_backend.sh
list(APPEND FLUTTER_TOOL_ENVIRONMENT
  "FLUTTER_ROOT=E:\\utils\\flutter"
  "PROJECT_DIR=D:\\PROGRAMMING\\AC-FIT\\acfit_app"
  "FLUTTER_ROOT=E:\\utils\\flutter"
  "FLUTTER_EPHEMERAL_DIR=D:\\PROGRAMMING\\AC-FIT\\acfit_app\\windows\\flutter\\ephemeral"
  "PROJECT_DIR=D:\\PROGRAMMING\\AC-FIT\\acfit_app"
  "FLUTTER_TARGET=D:\\PROGRAMMING\\AC-FIT\\acfit_app\\lib\\main.dart"
  "DART_DEFINES=RkxVVFRFUl9WRVJTSU9OPTMuMzIuMQ==,RkxVVFRFUl9DSEFOTkVMPXN0YWJsZQ==,RkxVVFRFUl9HSVRfVVJMPWh0dHBzOi8vZ2l0aHViLmNvbS9mbHV0dGVyL2ZsdXR0ZXIuZ2l0,RkxVVFRFUl9GUkFNRVdPUktfUkVWSVNJT049YjI1MzA1YTg4Mw==,RkxVVFRFUl9FTkdJTkVfUkVWSVNJT049MTQyNWU1ZTllYw==,RkxVVFRFUl9EQVJUX1ZFUlNJT049My44LjE="
  "DART_OBFUSCATION=false"
  "TRACK_WIDGET_CREATION=true"
  "TREE_SHAKE_ICONS=false"
  "PACKAGE_CONFIG=D:\\PROGRAMMING\\AC-FIT\\acfit_app\\.dart_tool\\package_config.json"
)
