{"logs": [{"outputFile": "com.example.acfit_app-mergeReleaseResources-49:/values-v23/values-v23.xml", "map": [{"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.10.2\\transforms\\8975063235ea0ba114f3bb0240765838\\transformed\\work-runtime-2.8.1\\res\\values-v23\\values-v23.xml", "from": {"startLines": "2,3", "startColumns": "4,4", "startOffsets": "55,121", "endColumns": "65,62", "endOffsets": "116,179"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.10.2\\transforms\\32d7d149835c8c00f0697dfcab9adb84\\transformed\\appcompat-1.1.0\\res\\values-v23\\values-v23.xml", "from": {"startLines": "2,3,4,5,6,20,34,35,36,37,41,42,43,44", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "55,190,325,400,487,1371,2267,2386,2513,2618,2842,2957,3064,3177", "endLines": "2,3,4,5,19,33,34,35,36,40,41,42,43,47", "endColumns": "134,134,74,86,12,12,118,126,104,12,114,106,112,12", "endOffsets": "185,320,395,482,1366,2262,2381,2508,2613,2837,2952,3059,3172,3402"}, "to": {"startLines": "4,5,6,7,8,22,36,37,38,39,43,44,45,46", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "184,319,454,529,616,1354,2104,2223,2350,2455,2679,2794,2901,3014", "endLines": "4,5,6,7,21,35,36,37,38,42,43,44,45,49", "endColumns": "134,134,74,86,12,12,118,126,104,12,114,106,112,12", "endOffsets": "314,449,524,611,1349,2099,2218,2345,2450,2674,2789,2896,3009,3239"}}]}]}