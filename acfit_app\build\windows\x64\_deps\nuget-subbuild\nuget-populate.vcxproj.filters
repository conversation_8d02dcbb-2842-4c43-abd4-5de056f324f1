﻿<?xml version="1.0" encoding="utf-8"?>
<Project ToolsVersion="17.0" xmlns="http://schemas.microsoft.com/developer/msbuild/2003">
  <ItemGroup>
    <CustomBuild Include="D:\PROGRAMMING\AC-FIT\acfit_app\build\windows\x64\_deps\nuget-subbuild\CMakeFiles\eb9f5a246f144237e4fef1670bf2f086\nuget-populate-mkdir.rule">
      <Filter>CMake Rules</Filter>
    </CustomBuild>
    <CustomBuild Include="D:\PROGRAMMING\AC-FIT\acfit_app\build\windows\x64\_deps\nuget-subbuild\CMakeFiles\eb9f5a246f144237e4fef1670bf2f086\nuget-populate-download.rule">
      <Filter>CMake Rules</Filter>
    </CustomBuild>
    <CustomBuild Include="D:\PROGRAMMING\AC-FIT\acfit_app\build\windows\x64\_deps\nuget-subbuild\CMakeFiles\eb9f5a246f144237e4fef1670bf2f086\nuget-populate-update.rule">
      <Filter>CMake Rules</Filter>
    </CustomBuild>
    <CustomBuild Include="D:\PROGRAMMING\AC-FIT\acfit_app\build\windows\x64\_deps\nuget-subbuild\CMakeFiles\eb9f5a246f144237e4fef1670bf2f086\nuget-populate-patch.rule">
      <Filter>CMake Rules</Filter>
    </CustomBuild>
    <CustomBuild Include="D:\PROGRAMMING\AC-FIT\acfit_app\build\windows\x64\_deps\nuget-subbuild\CMakeFiles\eb9f5a246f144237e4fef1670bf2f086\nuget-populate-copyfile.rule">
      <Filter>CMake Rules</Filter>
    </CustomBuild>
    <CustomBuild Include="D:\PROGRAMMING\AC-FIT\acfit_app\build\windows\x64\_deps\nuget-subbuild\CMakeFiles\eb9f5a246f144237e4fef1670bf2f086\nuget-populate-configure.rule">
      <Filter>CMake Rules</Filter>
    </CustomBuild>
    <CustomBuild Include="D:\PROGRAMMING\AC-FIT\acfit_app\build\windows\x64\_deps\nuget-subbuild\CMakeFiles\eb9f5a246f144237e4fef1670bf2f086\nuget-populate-build.rule">
      <Filter>CMake Rules</Filter>
    </CustomBuild>
    <CustomBuild Include="D:\PROGRAMMING\AC-FIT\acfit_app\build\windows\x64\_deps\nuget-subbuild\CMakeFiles\eb9f5a246f144237e4fef1670bf2f086\nuget-populate-install.rule">
      <Filter>CMake Rules</Filter>
    </CustomBuild>
    <CustomBuild Include="D:\PROGRAMMING\AC-FIT\acfit_app\build\windows\x64\_deps\nuget-subbuild\CMakeFiles\eb9f5a246f144237e4fef1670bf2f086\nuget-populate-test.rule">
      <Filter>CMake Rules</Filter>
    </CustomBuild>
    <CustomBuild Include="D:\PROGRAMMING\AC-FIT\acfit_app\build\windows\x64\_deps\nuget-subbuild\CMakeFiles\06c79a50e8199cf0f5c9ef9fd3c2d983\nuget-populate-complete.rule">
      <Filter>CMake Rules</Filter>
    </CustomBuild>
    <CustomBuild Include="D:\PROGRAMMING\AC-FIT\acfit_app\build\windows\x64\_deps\nuget-subbuild\CMakeFiles\6b24c575f1bd7905b0093f2da6d11553\nuget-populate.rule">
      <Filter>CMake Rules</Filter>
    </CustomBuild>
    <CustomBuild Include="D:\PROGRAMMING\AC-FIT\acfit_app\build\windows\x64\_deps\nuget-subbuild\CMakeLists.txt" />
  </ItemGroup>
  <ItemGroup>
    <None Include="D:\PROGRAMMING\AC-FIT\acfit_app\build\windows\x64\_deps\nuget-subbuild\CMakeFiles\nuget-populate" />
  </ItemGroup>
  <ItemGroup>
    <Filter Include="CMake Rules">
      <UniqueIdentifier>{0CCA8A4E-0D2D-3963-865D-990FCD61F741}</UniqueIdentifier>
    </Filter>
  </ItemGroup>
</Project>
