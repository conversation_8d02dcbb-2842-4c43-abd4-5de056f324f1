﻿
Microsoft Visual Studio Solution File, Format Version 12.00
# Visual Studio Version 17
Project("{2150E333-8FDC-42A3-9474-1A3956D46DE8}") = "CMakePredefinedTargets", "CMakePredefinedTargets", "{AA0AA025-9BAE-3352-AB86-A6968D25EDD3}"
EndProject
Project("{2150E333-8FDC-42A3-9474-1A3956D46DE8}") = "ExternalProjectTargets", "ExternalProjectTargets", "{A8D8C09A-DD6B-3931-AC11-AF580B27BEF7}"
EndProject
Project("{2150E333-8FDC-42A3-9474-1A3956D46DE8}") = "nuget-populate", "ExternalProjectTargets\nuget-populate", "{FB19FD3C-4B12-3598-AAFA-570CE8A9DEF4}"
EndProject
Project("{8BC9CEB8-8B4A-11D0-8D11-00A0C91BC942}") = "ALL_BUILD", "ALL_BUILD.vcxproj", "{FDEE44B0-C1B5-3FB3-9841-4D57634418B7}"
	ProjectSection(ProjectDependencies) = postProject
		{140EEE89-3986-3F0B-BD96-F907DB85EE1B} = {140EEE89-3986-3F0B-BD96-F907DB85EE1B}
		{26063113-6F84-366F-9374-09E56B5B3428} = {26063113-6F84-366F-9374-09E56B5B3428}
	EndProjectSection
EndProject
Project("{8BC9CEB8-8B4A-11D0-8D11-00A0C91BC942}") = "ZERO_CHECK", "ZERO_CHECK.vcxproj", "{140EEE89-3986-3F0B-BD96-F907DB85EE1B}"
	ProjectSection(ProjectDependencies) = postProject
	EndProjectSection
EndProject
Project("{8BC9CEB8-8B4A-11D0-8D11-00A0C91BC942}") = "nuget-populate", "nuget-populate.vcxproj", "{26063113-6F84-366F-9374-09E56B5B3428}"
	ProjectSection(ProjectDependencies) = postProject
		{140EEE89-3986-3F0B-BD96-F907DB85EE1B} = {140EEE89-3986-3F0B-BD96-F907DB85EE1B}
	EndProjectSection
EndProject
Global
	GlobalSection(SolutionConfigurationPlatforms) = preSolution
		Debug|x64 = Debug|x64
	EndGlobalSection
	GlobalSection(ProjectConfigurationPlatforms) = postSolution
		{FDEE44B0-C1B5-3FB3-9841-4D57634418B7}.Debug|x64.ActiveCfg = Debug|x64
		{140EEE89-3986-3F0B-BD96-F907DB85EE1B}.Debug|x64.ActiveCfg = Debug|x64
		{140EEE89-3986-3F0B-BD96-F907DB85EE1B}.Debug|x64.Build.0 = Debug|x64
		{26063113-6F84-366F-9374-09E56B5B3428}.Debug|x64.ActiveCfg = Debug|x64
		{26063113-6F84-366F-9374-09E56B5B3428}.Debug|x64.Build.0 = Debug|x64
	EndGlobalSection
	GlobalSection(NestedProjects) = preSolution
		{FDEE44B0-C1B5-3FB3-9841-4D57634418B7} = {AA0AA025-9BAE-3352-AB86-A6968D25EDD3}
		{140EEE89-3986-3F0B-BD96-F907DB85EE1B} = {AA0AA025-9BAE-3352-AB86-A6968D25EDD3}
		{FB19FD3C-4B12-3598-AAFA-570CE8A9DEF4} = {A8D8C09A-DD6B-3931-AC11-AF580B27BEF7}
		{26063113-6F84-366F-9374-09E56B5B3428} = {FB19FD3C-4B12-3598-AAFA-570CE8A9DEF4}
	EndGlobalSection
	GlobalSection(ExtensibilityGlobals) = postSolution
		SolutionGuid = {E263D3D7-B810-3F4E-9004-B9421F1628B7}
	EndGlobalSection
	GlobalSection(ExtensibilityAddIns) = postSolution
	EndGlobalSection
EndGlobal
