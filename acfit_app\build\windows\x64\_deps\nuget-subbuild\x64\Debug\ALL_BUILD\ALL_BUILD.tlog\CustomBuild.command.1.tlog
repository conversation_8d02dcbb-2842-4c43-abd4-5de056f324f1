^D:\PROGRAMMING\AC-FIT\ACFIT_APP\BUILD\WINDOWS\X64\_DEPS\NUGET-SUBBUILD\CMAKELISTS.TXT
setlocal
"C:\Program Files\Microsoft Visual Studio\2022\Community\Common7\IDE\CommonExtensions\Microsoft\CMake\CMake\bin\cmake.exe" -SD:/PROGRAMMING/AC-FIT/acfit_app/build/windows/x64/_deps/nuget-subbuild -BD:/PROGRAMMING/AC-FIT/acfit_app/build/windows/x64/_deps/nuget-subbuild --check-stamp-file D:/PROGRAMMING/AC-FIT/acfit_app/build/windows/x64/_deps/nuget-subbuild/CMakeFiles/generate.stamp
if %errorlevel% neq 0 goto :cmEnd
:cmEnd
endlocal & call :cmErrorLevel %errorlevel% & goto :cmDone
:cmErrorLevel
exit /b %1
:cmDone
if %errorlevel% neq 0 goto :VCEnd
