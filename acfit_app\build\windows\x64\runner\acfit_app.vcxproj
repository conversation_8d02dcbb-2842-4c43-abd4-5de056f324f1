﻿<?xml version="1.0" encoding="utf-8"?>
<Project DefaultTargets="Build" ToolsVersion="17.0" xmlns="http://schemas.microsoft.com/developer/msbuild/2003">
  <PropertyGroup>
    <PreferredToolArchitecture>x64</PreferredToolArchitecture>
  </PropertyGroup>
  <ItemGroup Label="ProjectConfigurations">
    <ProjectConfiguration Include="Debug|x64">
      <Configuration>Debug</Configuration>
      <Platform>x64</Platform>
    </ProjectConfiguration>
    <ProjectConfiguration Include="Profile|x64">
      <Configuration>Profile</Configuration>
      <Platform>x64</Platform>
    </ProjectConfiguration>
    <ProjectConfiguration Include="Release|x64">
      <Configuration>Release</Configuration>
      <Platform>x64</Platform>
    </ProjectConfiguration>
  </ItemGroup>
  <PropertyGroup Label="Globals">
    <ProjectGuid>{4208F15F-86AF-395E-A3DA-F51B1F0B81FE}</ProjectGuid>
    <Keyword>Win32Proj</Keyword>
    <WindowsTargetPlatformVersion>10.0.26100.0</WindowsTargetPlatformVersion>
    <Platform>x64</Platform>
    <ProjectName>acfit_app</ProjectName>
    <VCProjectUpgraderObjectName>NoUpgrade</VCProjectUpgraderObjectName>
  </PropertyGroup>
  <Import Project="$(VCTargetsPath)\Microsoft.Cpp.Default.props" />
  <PropertyGroup Condition="'$(Configuration)|$(Platform)'=='Debug|x64'" Label="Configuration">
    <ConfigurationType>Application</ConfigurationType>
    <CharacterSet>Unicode</CharacterSet>
    <PlatformToolset>v143</PlatformToolset>
  </PropertyGroup>
  <PropertyGroup Condition="'$(Configuration)|$(Platform)'=='Profile|x64'" Label="Configuration">
    <ConfigurationType>Application</ConfigurationType>
    <CharacterSet>Unicode</CharacterSet>
    <PlatformToolset>v143</PlatformToolset>
  </PropertyGroup>
  <PropertyGroup Condition="'$(Configuration)|$(Platform)'=='Release|x64'" Label="Configuration">
    <ConfigurationType>Application</ConfigurationType>
    <CharacterSet>Unicode</CharacterSet>
    <PlatformToolset>v143</PlatformToolset>
  </PropertyGroup>
  <Import Project="$(VCTargetsPath)\Microsoft.Cpp.props" />
  <ImportGroup Label="ExtensionSettings">
  </ImportGroup>
  <ImportGroup Label="PropertySheets">
    <Import Project="$(UserRootDir)\Microsoft.Cpp.$(Platform).user.props" Condition="exists('$(UserRootDir)\Microsoft.Cpp.$(Platform).user.props')" Label="LocalAppDataPlatform" />
  </ImportGroup>
  <PropertyGroup Label="UserMacros" />
  <PropertyGroup>
    <_ProjectFileVersion>10.0.20506.1</_ProjectFileVersion>
    <OutDir Condition="'$(Configuration)|$(Platform)'=='Debug|x64'">D:\PROGRAMMING\AC-FIT\acfit_app\build\windows\x64\runner\Debug\</OutDir>
    <IntDir Condition="'$(Configuration)|$(Platform)'=='Debug|x64'">acfit_app.dir\Debug\</IntDir>
    <TargetName Condition="'$(Configuration)|$(Platform)'=='Debug|x64'">acfit_app</TargetName>
    <TargetExt Condition="'$(Configuration)|$(Platform)'=='Debug|x64'">.exe</TargetExt>
    <LinkIncremental Condition="'$(Configuration)|$(Platform)'=='Debug|x64'">true</LinkIncremental>
    <GenerateManifest Condition="'$(Configuration)|$(Platform)'=='Debug|x64'">true</GenerateManifest>
    <OutDir Condition="'$(Configuration)|$(Platform)'=='Profile|x64'">D:\PROGRAMMING\AC-FIT\acfit_app\build\windows\x64\runner\Profile\</OutDir>
    <IntDir Condition="'$(Configuration)|$(Platform)'=='Profile|x64'">acfit_app.dir\Profile\</IntDir>
    <TargetName Condition="'$(Configuration)|$(Platform)'=='Profile|x64'">acfit_app</TargetName>
    <TargetExt Condition="'$(Configuration)|$(Platform)'=='Profile|x64'">.exe</TargetExt>
    <LinkIncremental Condition="'$(Configuration)|$(Platform)'=='Profile|x64'">false</LinkIncremental>
    <GenerateManifest Condition="'$(Configuration)|$(Platform)'=='Profile|x64'">true</GenerateManifest>
    <OutDir Condition="'$(Configuration)|$(Platform)'=='Release|x64'">D:\PROGRAMMING\AC-FIT\acfit_app\build\windows\x64\runner\Release\</OutDir>
    <IntDir Condition="'$(Configuration)|$(Platform)'=='Release|x64'">acfit_app.dir\Release\</IntDir>
    <TargetName Condition="'$(Configuration)|$(Platform)'=='Release|x64'">acfit_app</TargetName>
    <TargetExt Condition="'$(Configuration)|$(Platform)'=='Release|x64'">.exe</TargetExt>
    <LinkIncremental Condition="'$(Configuration)|$(Platform)'=='Release|x64'">false</LinkIncremental>
    <GenerateManifest Condition="'$(Configuration)|$(Platform)'=='Release|x64'">true</GenerateManifest>
  </PropertyGroup>
  <ItemDefinitionGroup Condition="'$(Configuration)|$(Platform)'=='Debug|x64'">
    <ClCompile>
      <AdditionalIncludeDirectories>D:\PROGRAMMING\AC-FIT\acfit_app\windows;D:\PROGRAMMING\AC-FIT\acfit_app\windows\flutter\ephemeral;D:\PROGRAMMING\AC-FIT\acfit_app\windows\flutter\ephemeral\cpp_client_wrapper\include;D:\PROGRAMMING\AC-FIT\acfit_app\windows\flutter\ephemeral\.plugin_symlinks\audioplayers_windows\windows\include;D:\PROGRAMMING\AC-FIT\acfit_app\windows\flutter\ephemeral\.plugin_symlinks\connectivity_plus\windows\include;D:\PROGRAMMING\AC-FIT\acfit_app\windows\flutter\ephemeral\.plugin_symlinks\file_selector_windows\windows\include;D:\PROGRAMMING\AC-FIT\acfit_app\windows\flutter\ephemeral\.plugin_symlinks\flutter_secure_storage_windows\windows\include;D:\PROGRAMMING\AC-FIT\acfit_app\windows\flutter\ephemeral\.plugin_symlinks\flutter_timezone\windows\include;D:\PROGRAMMING\AC-FIT\acfit_app\windows\flutter\ephemeral\.plugin_symlinks\url_launcher_windows\windows\include;%(AdditionalIncludeDirectories)</AdditionalIncludeDirectories>
      <AssemblerListingLocation>$(IntDir)</AssemblerListingLocation>
      <BasicRuntimeChecks>EnableFastChecks</BasicRuntimeChecks>
      <DebugInformationFormat>ProgramDatabase</DebugInformationFormat>
      <DisableSpecificWarnings>"4100"</DisableSpecificWarnings>
      <ExceptionHandling>Sync</ExceptionHandling>
      <InlineFunctionExpansion>Disabled</InlineFunctionExpansion>
      <LanguageStandard>stdcpp17</LanguageStandard>
      <MinimalRebuild></MinimalRebuild>
      <Optimization>Disabled</Optimization>
      <PrecompiledHeader>NotUsing</PrecompiledHeader>
      <RuntimeLibrary>MultiThreadedDebugDLL</RuntimeLibrary>
      <RuntimeTypeInfo>true</RuntimeTypeInfo>
      <SupportJustMyCode></SupportJustMyCode>
      <TreatWarningAsError>true</TreatWarningAsError>
      <UseFullPaths>false</UseFullPaths>
      <WarningLevel>Level4</WarningLevel>
      <PreprocessorDefinitions>%(PreprocessorDefinitions);WIN32;_WINDOWS;_HAS_EXCEPTIONS=0;_DEBUG;FLUTTER_VERSION="0.1.0";FLUTTER_VERSION_MAJOR=0;FLUTTER_VERSION_MINOR=1;FLUTTER_VERSION_PATCH=0;FLUTTER_VERSION_BUILD=0;NOMINMAX;UNICODE;_UNICODE;CMAKE_INTDIR="Debug"</PreprocessorDefinitions>
      <ObjectFileName>$(IntDir)</ObjectFileName>
      <ScanSourceForModuleDependencies>false</ScanSourceForModuleDependencies>
    </ClCompile>
    <ResourceCompile>
      <PreprocessorDefinitions>%(PreprocessorDefinitions);WIN32;_DEBUG;_WINDOWS;_HAS_EXCEPTIONS=0;FLUTTER_VERSION=\"0.1.0\";FLUTTER_VERSION_MAJOR=0;FLUTTER_VERSION_MINOR=1;FLUTTER_VERSION_PATCH=0;FLUTTER_VERSION_BUILD=0;NOMINMAX;UNICODE;_UNICODE;CMAKE_INTDIR=\"Debug\"</PreprocessorDefinitions>
      <AdditionalIncludeDirectories>D:\PROGRAMMING\AC-FIT\acfit_app\windows;D:\PROGRAMMING\AC-FIT\acfit_app\windows\flutter\ephemeral;D:\PROGRAMMING\AC-FIT\acfit_app\windows\flutter\ephemeral\cpp_client_wrapper\include;D:\PROGRAMMING\AC-FIT\acfit_app\windows\flutter\ephemeral\.plugin_symlinks\audioplayers_windows\windows\include;D:\PROGRAMMING\AC-FIT\acfit_app\windows\flutter\ephemeral\.plugin_symlinks\connectivity_plus\windows\include;D:\PROGRAMMING\AC-FIT\acfit_app\windows\flutter\ephemeral\.plugin_symlinks\file_selector_windows\windows\include;D:\PROGRAMMING\AC-FIT\acfit_app\windows\flutter\ephemeral\.plugin_symlinks\flutter_secure_storage_windows\windows\include;D:\PROGRAMMING\AC-FIT\acfit_app\windows\flutter\ephemeral\.plugin_symlinks\flutter_timezone\windows\include;D:\PROGRAMMING\AC-FIT\acfit_app\windows\flutter\ephemeral\.plugin_symlinks\url_launcher_windows\windows\include;%(AdditionalIncludeDirectories)</AdditionalIncludeDirectories>
    </ResourceCompile>
    <Midl>
      <AdditionalIncludeDirectories>D:\PROGRAMMING\AC-FIT\acfit_app\windows;D:\PROGRAMMING\AC-FIT\acfit_app\windows\flutter\ephemeral;D:\PROGRAMMING\AC-FIT\acfit_app\windows\flutter\ephemeral\cpp_client_wrapper\include;D:\PROGRAMMING\AC-FIT\acfit_app\windows\flutter\ephemeral\.plugin_symlinks\audioplayers_windows\windows\include;D:\PROGRAMMING\AC-FIT\acfit_app\windows\flutter\ephemeral\.plugin_symlinks\connectivity_plus\windows\include;D:\PROGRAMMING\AC-FIT\acfit_app\windows\flutter\ephemeral\.plugin_symlinks\file_selector_windows\windows\include;D:\PROGRAMMING\AC-FIT\acfit_app\windows\flutter\ephemeral\.plugin_symlinks\flutter_secure_storage_windows\windows\include;D:\PROGRAMMING\AC-FIT\acfit_app\windows\flutter\ephemeral\.plugin_symlinks\flutter_timezone\windows\include;D:\PROGRAMMING\AC-FIT\acfit_app\windows\flutter\ephemeral\.plugin_symlinks\url_launcher_windows\windows\include;%(AdditionalIncludeDirectories)</AdditionalIncludeDirectories>
      <OutputDirectory>$(ProjectDir)/$(IntDir)</OutputDirectory>
      <HeaderFileName>%(Filename).h</HeaderFileName>
      <TypeLibraryName>%(Filename).tlb</TypeLibraryName>
      <InterfaceIdentifierFileName>%(Filename)_i.c</InterfaceIdentifierFileName>
      <ProxyFileName>%(Filename)_p.c</ProxyFileName>
    </Midl>
    <Link>
      <AdditionalDependencies>..\flutter\Debug\flutter_wrapper_app.lib;dwmapi.lib;..\plugins\audioplayers_windows\Debug\audioplayers_windows_plugin.lib;..\plugins\connectivity_plus\Debug\connectivity_plus_plugin.lib;..\plugins\file_selector_windows\Debug\file_selector_windows_plugin.lib;..\plugins\flutter_secure_storage_windows\Debug\flutter_secure_storage_windows_plugin.lib;..\plugins\flutter_timezone\Debug\flutter_timezone_plugin.lib;..\plugins\url_launcher_windows\Debug\url_launcher_windows_plugin.lib;D:\PROGRAMMING\AC-FIT\acfit_app\windows\flutter\ephemeral\flutter_windows.dll.lib;kernel32.lib;user32.lib;gdi32.lib;winspool.lib;shell32.lib;ole32.lib;oleaut32.lib;uuid.lib;comdlg32.lib;advapi32.lib</AdditionalDependencies>
      <AdditionalLibraryDirectories>%(AdditionalLibraryDirectories)</AdditionalLibraryDirectories>
      <AdditionalOptions>%(AdditionalOptions) /machine:x64</AdditionalOptions>
      <GenerateDebugInformation>true</GenerateDebugInformation>
      <IgnoreSpecificDefaultLibraries>%(IgnoreSpecificDefaultLibraries)</IgnoreSpecificDefaultLibraries>
      <ImportLibrary>D:/PROGRAMMING/AC-FIT/acfit_app/build/windows/x64/runner/Debug/acfit_app.lib</ImportLibrary>
      <ProgramDataBaseFile>D:/PROGRAMMING/AC-FIT/acfit_app/build/windows/x64/runner/Debug/acfit_app.pdb</ProgramDataBaseFile>
      <SubSystem>Windows</SubSystem>
    </Link>
    <ProjectReference>
      <LinkLibraryDependencies>false</LinkLibraryDependencies>
    </ProjectReference>
    <Manifest>
      <AdditionalManifestFiles>D:\PROGRAMMING\AC-FIT\acfit_app\windows\runner\runner.exe.manifest;</AdditionalManifestFiles>
    </Manifest>
  </ItemDefinitionGroup>
  <ItemDefinitionGroup Condition="'$(Configuration)|$(Platform)'=='Profile|x64'">
    <ClCompile>
      <AdditionalIncludeDirectories>D:\PROGRAMMING\AC-FIT\acfit_app\windows;D:\PROGRAMMING\AC-FIT\acfit_app\windows\flutter\ephemeral;D:\PROGRAMMING\AC-FIT\acfit_app\windows\flutter\ephemeral\cpp_client_wrapper\include;D:\PROGRAMMING\AC-FIT\acfit_app\windows\flutter\ephemeral\.plugin_symlinks\audioplayers_windows\windows\include;D:\PROGRAMMING\AC-FIT\acfit_app\windows\flutter\ephemeral\.plugin_symlinks\connectivity_plus\windows\include;D:\PROGRAMMING\AC-FIT\acfit_app\windows\flutter\ephemeral\.plugin_symlinks\file_selector_windows\windows\include;D:\PROGRAMMING\AC-FIT\acfit_app\windows\flutter\ephemeral\.plugin_symlinks\flutter_secure_storage_windows\windows\include;D:\PROGRAMMING\AC-FIT\acfit_app\windows\flutter\ephemeral\.plugin_symlinks\flutter_timezone\windows\include;D:\PROGRAMMING\AC-FIT\acfit_app\windows\flutter\ephemeral\.plugin_symlinks\url_launcher_windows\windows\include;%(AdditionalIncludeDirectories)</AdditionalIncludeDirectories>
      <AssemblerListingLocation>$(IntDir)</AssemblerListingLocation>
      <BasicRuntimeChecks>Default</BasicRuntimeChecks>
      <DisableSpecificWarnings>"4100"</DisableSpecificWarnings>
      <ExceptionHandling>Sync</ExceptionHandling>
      <InlineFunctionExpansion>AnySuitable</InlineFunctionExpansion>
      <LanguageStandard>stdcpp17</LanguageStandard>
      <MinimalRebuild></MinimalRebuild>
      <Optimization>MaxSpeed</Optimization>
      <PrecompiledHeader>NotUsing</PrecompiledHeader>
      <RuntimeLibrary>MultiThreadedDLL</RuntimeLibrary>
      <RuntimeTypeInfo>true</RuntimeTypeInfo>
      <SupportJustMyCode></SupportJustMyCode>
      <TreatWarningAsError>true</TreatWarningAsError>
      <UseFullPaths>false</UseFullPaths>
      <WarningLevel>Level4</WarningLevel>
      <PreprocessorDefinitions>%(PreprocessorDefinitions);WIN32;_WINDOWS;NDEBUG;_HAS_EXCEPTIONS=0;FLUTTER_VERSION="0.1.0";FLUTTER_VERSION_MAJOR=0;FLUTTER_VERSION_MINOR=1;FLUTTER_VERSION_PATCH=0;FLUTTER_VERSION_BUILD=0;NOMINMAX;UNICODE;_UNICODE;CMAKE_INTDIR="Profile"</PreprocessorDefinitions>
      <ObjectFileName>$(IntDir)</ObjectFileName>
      <DebugInformationFormat>
      </DebugInformationFormat>
      <ScanSourceForModuleDependencies>false</ScanSourceForModuleDependencies>
    </ClCompile>
    <ResourceCompile>
      <PreprocessorDefinitions>%(PreprocessorDefinitions);WIN32;_WINDOWS;NDEBUG;_HAS_EXCEPTIONS=0;FLUTTER_VERSION=\"0.1.0\";FLUTTER_VERSION_MAJOR=0;FLUTTER_VERSION_MINOR=1;FLUTTER_VERSION_PATCH=0;FLUTTER_VERSION_BUILD=0;NOMINMAX;UNICODE;_UNICODE;CMAKE_INTDIR=\"Profile\"</PreprocessorDefinitions>
      <AdditionalIncludeDirectories>D:\PROGRAMMING\AC-FIT\acfit_app\windows;D:\PROGRAMMING\AC-FIT\acfit_app\windows\flutter\ephemeral;D:\PROGRAMMING\AC-FIT\acfit_app\windows\flutter\ephemeral\cpp_client_wrapper\include;D:\PROGRAMMING\AC-FIT\acfit_app\windows\flutter\ephemeral\.plugin_symlinks\audioplayers_windows\windows\include;D:\PROGRAMMING\AC-FIT\acfit_app\windows\flutter\ephemeral\.plugin_symlinks\connectivity_plus\windows\include;D:\PROGRAMMING\AC-FIT\acfit_app\windows\flutter\ephemeral\.plugin_symlinks\file_selector_windows\windows\include;D:\PROGRAMMING\AC-FIT\acfit_app\windows\flutter\ephemeral\.plugin_symlinks\flutter_secure_storage_windows\windows\include;D:\PROGRAMMING\AC-FIT\acfit_app\windows\flutter\ephemeral\.plugin_symlinks\flutter_timezone\windows\include;D:\PROGRAMMING\AC-FIT\acfit_app\windows\flutter\ephemeral\.plugin_symlinks\url_launcher_windows\windows\include;%(AdditionalIncludeDirectories)</AdditionalIncludeDirectories>
    </ResourceCompile>
    <Midl>
      <AdditionalIncludeDirectories>D:\PROGRAMMING\AC-FIT\acfit_app\windows;D:\PROGRAMMING\AC-FIT\acfit_app\windows\flutter\ephemeral;D:\PROGRAMMING\AC-FIT\acfit_app\windows\flutter\ephemeral\cpp_client_wrapper\include;D:\PROGRAMMING\AC-FIT\acfit_app\windows\flutter\ephemeral\.plugin_symlinks\audioplayers_windows\windows\include;D:\PROGRAMMING\AC-FIT\acfit_app\windows\flutter\ephemeral\.plugin_symlinks\connectivity_plus\windows\include;D:\PROGRAMMING\AC-FIT\acfit_app\windows\flutter\ephemeral\.plugin_symlinks\file_selector_windows\windows\include;D:\PROGRAMMING\AC-FIT\acfit_app\windows\flutter\ephemeral\.plugin_symlinks\flutter_secure_storage_windows\windows\include;D:\PROGRAMMING\AC-FIT\acfit_app\windows\flutter\ephemeral\.plugin_symlinks\flutter_timezone\windows\include;D:\PROGRAMMING\AC-FIT\acfit_app\windows\flutter\ephemeral\.plugin_symlinks\url_launcher_windows\windows\include;%(AdditionalIncludeDirectories)</AdditionalIncludeDirectories>
      <OutputDirectory>$(ProjectDir)/$(IntDir)</OutputDirectory>
      <HeaderFileName>%(Filename).h</HeaderFileName>
      <TypeLibraryName>%(Filename).tlb</TypeLibraryName>
      <InterfaceIdentifierFileName>%(Filename)_i.c</InterfaceIdentifierFileName>
      <ProxyFileName>%(Filename)_p.c</ProxyFileName>
    </Midl>
    <Link>
      <AdditionalDependencies>..\flutter\Profile\flutter_wrapper_app.lib;dwmapi.lib;..\plugins\audioplayers_windows\Profile\audioplayers_windows_plugin.lib;..\plugins\connectivity_plus\Profile\connectivity_plus_plugin.lib;..\plugins\file_selector_windows\Profile\file_selector_windows_plugin.lib;..\plugins\flutter_secure_storage_windows\Profile\flutter_secure_storage_windows_plugin.lib;..\plugins\flutter_timezone\Profile\flutter_timezone_plugin.lib;..\plugins\url_launcher_windows\Profile\url_launcher_windows_plugin.lib;D:\PROGRAMMING\AC-FIT\acfit_app\windows\flutter\ephemeral\flutter_windows.dll.lib;kernel32.lib;user32.lib;gdi32.lib;winspool.lib;shell32.lib;ole32.lib;oleaut32.lib;uuid.lib;comdlg32.lib;advapi32.lib</AdditionalDependencies>
      <AdditionalLibraryDirectories>%(AdditionalLibraryDirectories)</AdditionalLibraryDirectories>
      <AdditionalOptions>%(AdditionalOptions) /machine:x64</AdditionalOptions>
      <GenerateDebugInformation>false</GenerateDebugInformation>
      <IgnoreSpecificDefaultLibraries>%(IgnoreSpecificDefaultLibraries)</IgnoreSpecificDefaultLibraries>
      <ImportLibrary>D:/PROGRAMMING/AC-FIT/acfit_app/build/windows/x64/runner/Profile/acfit_app.lib</ImportLibrary>
      <ProgramDataBaseFile>D:/PROGRAMMING/AC-FIT/acfit_app/build/windows/x64/runner/Profile/acfit_app.pdb</ProgramDataBaseFile>
      <SubSystem>Windows</SubSystem>
    </Link>
    <ProjectReference>
      <LinkLibraryDependencies>false</LinkLibraryDependencies>
    </ProjectReference>
    <Manifest>
      <AdditionalManifestFiles>D:\PROGRAMMING\AC-FIT\acfit_app\windows\runner\runner.exe.manifest;</AdditionalManifestFiles>
    </Manifest>
  </ItemDefinitionGroup>
  <ItemDefinitionGroup Condition="'$(Configuration)|$(Platform)'=='Release|x64'">
    <ClCompile>
      <AdditionalIncludeDirectories>D:\PROGRAMMING\AC-FIT\acfit_app\windows;D:\PROGRAMMING\AC-FIT\acfit_app\windows\flutter\ephemeral;D:\PROGRAMMING\AC-FIT\acfit_app\windows\flutter\ephemeral\cpp_client_wrapper\include;D:\PROGRAMMING\AC-FIT\acfit_app\windows\flutter\ephemeral\.plugin_symlinks\audioplayers_windows\windows\include;D:\PROGRAMMING\AC-FIT\acfit_app\windows\flutter\ephemeral\.plugin_symlinks\connectivity_plus\windows\include;D:\PROGRAMMING\AC-FIT\acfit_app\windows\flutter\ephemeral\.plugin_symlinks\file_selector_windows\windows\include;D:\PROGRAMMING\AC-FIT\acfit_app\windows\flutter\ephemeral\.plugin_symlinks\flutter_secure_storage_windows\windows\include;D:\PROGRAMMING\AC-FIT\acfit_app\windows\flutter\ephemeral\.plugin_symlinks\flutter_timezone\windows\include;D:\PROGRAMMING\AC-FIT\acfit_app\windows\flutter\ephemeral\.plugin_symlinks\url_launcher_windows\windows\include;%(AdditionalIncludeDirectories)</AdditionalIncludeDirectories>
      <AssemblerListingLocation>$(IntDir)</AssemblerListingLocation>
      <BasicRuntimeChecks>Default</BasicRuntimeChecks>
      <DisableSpecificWarnings>"4100"</DisableSpecificWarnings>
      <ExceptionHandling>Sync</ExceptionHandling>
      <InlineFunctionExpansion>AnySuitable</InlineFunctionExpansion>
      <LanguageStandard>stdcpp17</LanguageStandard>
      <MinimalRebuild></MinimalRebuild>
      <Optimization>MaxSpeed</Optimization>
      <PrecompiledHeader>NotUsing</PrecompiledHeader>
      <RuntimeLibrary>MultiThreadedDLL</RuntimeLibrary>
      <RuntimeTypeInfo>true</RuntimeTypeInfo>
      <SupportJustMyCode></SupportJustMyCode>
      <TreatWarningAsError>true</TreatWarningAsError>
      <UseFullPaths>false</UseFullPaths>
      <WarningLevel>Level4</WarningLevel>
      <PreprocessorDefinitions>%(PreprocessorDefinitions);WIN32;_WINDOWS;NDEBUG;_HAS_EXCEPTIONS=0;FLUTTER_VERSION="0.1.0";FLUTTER_VERSION_MAJOR=0;FLUTTER_VERSION_MINOR=1;FLUTTER_VERSION_PATCH=0;FLUTTER_VERSION_BUILD=0;NOMINMAX;UNICODE;_UNICODE;CMAKE_INTDIR="Release"</PreprocessorDefinitions>
      <ObjectFileName>$(IntDir)</ObjectFileName>
      <DebugInformationFormat>
      </DebugInformationFormat>
      <ScanSourceForModuleDependencies>false</ScanSourceForModuleDependencies>
    </ClCompile>
    <ResourceCompile>
      <PreprocessorDefinitions>%(PreprocessorDefinitions);WIN32;_WINDOWS;NDEBUG;_HAS_EXCEPTIONS=0;FLUTTER_VERSION=\"0.1.0\";FLUTTER_VERSION_MAJOR=0;FLUTTER_VERSION_MINOR=1;FLUTTER_VERSION_PATCH=0;FLUTTER_VERSION_BUILD=0;NOMINMAX;UNICODE;_UNICODE;CMAKE_INTDIR=\"Release\"</PreprocessorDefinitions>
      <AdditionalIncludeDirectories>D:\PROGRAMMING\AC-FIT\acfit_app\windows;D:\PROGRAMMING\AC-FIT\acfit_app\windows\flutter\ephemeral;D:\PROGRAMMING\AC-FIT\acfit_app\windows\flutter\ephemeral\cpp_client_wrapper\include;D:\PROGRAMMING\AC-FIT\acfit_app\windows\flutter\ephemeral\.plugin_symlinks\audioplayers_windows\windows\include;D:\PROGRAMMING\AC-FIT\acfit_app\windows\flutter\ephemeral\.plugin_symlinks\connectivity_plus\windows\include;D:\PROGRAMMING\AC-FIT\acfit_app\windows\flutter\ephemeral\.plugin_symlinks\file_selector_windows\windows\include;D:\PROGRAMMING\AC-FIT\acfit_app\windows\flutter\ephemeral\.plugin_symlinks\flutter_secure_storage_windows\windows\include;D:\PROGRAMMING\AC-FIT\acfit_app\windows\flutter\ephemeral\.plugin_symlinks\flutter_timezone\windows\include;D:\PROGRAMMING\AC-FIT\acfit_app\windows\flutter\ephemeral\.plugin_symlinks\url_launcher_windows\windows\include;%(AdditionalIncludeDirectories)</AdditionalIncludeDirectories>
    </ResourceCompile>
    <Midl>
      <AdditionalIncludeDirectories>D:\PROGRAMMING\AC-FIT\acfit_app\windows;D:\PROGRAMMING\AC-FIT\acfit_app\windows\flutter\ephemeral;D:\PROGRAMMING\AC-FIT\acfit_app\windows\flutter\ephemeral\cpp_client_wrapper\include;D:\PROGRAMMING\AC-FIT\acfit_app\windows\flutter\ephemeral\.plugin_symlinks\audioplayers_windows\windows\include;D:\PROGRAMMING\AC-FIT\acfit_app\windows\flutter\ephemeral\.plugin_symlinks\connectivity_plus\windows\include;D:\PROGRAMMING\AC-FIT\acfit_app\windows\flutter\ephemeral\.plugin_symlinks\file_selector_windows\windows\include;D:\PROGRAMMING\AC-FIT\acfit_app\windows\flutter\ephemeral\.plugin_symlinks\flutter_secure_storage_windows\windows\include;D:\PROGRAMMING\AC-FIT\acfit_app\windows\flutter\ephemeral\.plugin_symlinks\flutter_timezone\windows\include;D:\PROGRAMMING\AC-FIT\acfit_app\windows\flutter\ephemeral\.plugin_symlinks\url_launcher_windows\windows\include;%(AdditionalIncludeDirectories)</AdditionalIncludeDirectories>
      <OutputDirectory>$(ProjectDir)/$(IntDir)</OutputDirectory>
      <HeaderFileName>%(Filename).h</HeaderFileName>
      <TypeLibraryName>%(Filename).tlb</TypeLibraryName>
      <InterfaceIdentifierFileName>%(Filename)_i.c</InterfaceIdentifierFileName>
      <ProxyFileName>%(Filename)_p.c</ProxyFileName>
    </Midl>
    <Link>
      <AdditionalDependencies>..\flutter\Release\flutter_wrapper_app.lib;dwmapi.lib;..\plugins\audioplayers_windows\Release\audioplayers_windows_plugin.lib;..\plugins\connectivity_plus\Release\connectivity_plus_plugin.lib;..\plugins\file_selector_windows\Release\file_selector_windows_plugin.lib;..\plugins\flutter_secure_storage_windows\Release\flutter_secure_storage_windows_plugin.lib;..\plugins\flutter_timezone\Release\flutter_timezone_plugin.lib;..\plugins\url_launcher_windows\Release\url_launcher_windows_plugin.lib;D:\PROGRAMMING\AC-FIT\acfit_app\windows\flutter\ephemeral\flutter_windows.dll.lib;kernel32.lib;user32.lib;gdi32.lib;winspool.lib;shell32.lib;ole32.lib;oleaut32.lib;uuid.lib;comdlg32.lib;advapi32.lib</AdditionalDependencies>
      <AdditionalLibraryDirectories>%(AdditionalLibraryDirectories)</AdditionalLibraryDirectories>
      <AdditionalOptions>%(AdditionalOptions) /machine:x64</AdditionalOptions>
      <GenerateDebugInformation>false</GenerateDebugInformation>
      <IgnoreSpecificDefaultLibraries>%(IgnoreSpecificDefaultLibraries)</IgnoreSpecificDefaultLibraries>
      <ImportLibrary>D:/PROGRAMMING/AC-FIT/acfit_app/build/windows/x64/runner/Release/acfit_app.lib</ImportLibrary>
      <ProgramDataBaseFile>D:/PROGRAMMING/AC-FIT/acfit_app/build/windows/x64/runner/Release/acfit_app.pdb</ProgramDataBaseFile>
      <SubSystem>Windows</SubSystem>
    </Link>
    <ProjectReference>
      <LinkLibraryDependencies>false</LinkLibraryDependencies>
    </ProjectReference>
    <Manifest>
      <AdditionalManifestFiles>D:\PROGRAMMING\AC-FIT\acfit_app\windows\runner\runner.exe.manifest;</AdditionalManifestFiles>
    </Manifest>
  </ItemDefinitionGroup>
  <ItemGroup>
    <CustomBuild Include="D:\PROGRAMMING\AC-FIT\acfit_app\windows\runner\CMakeLists.txt">
      <UseUtf8Encoding>Always</UseUtf8Encoding>
      <Message Condition="'$(Configuration)|$(Platform)'=='Debug|x64'">Building Custom Rule D:/PROGRAMMING/AC-FIT/acfit_app/windows/runner/CMakeLists.txt</Message>
      <Command Condition="'$(Configuration)|$(Platform)'=='Debug|x64'">setlocal
"C:\Program Files\Microsoft Visual Studio\2022\Community\Common7\IDE\CommonExtensions\Microsoft\CMake\CMake\bin\cmake.exe" -SD:/PROGRAMMING/AC-FIT/acfit_app/windows -BD:/PROGRAMMING/AC-FIT/acfit_app/build/windows/x64 --check-stamp-file D:/PROGRAMMING/AC-FIT/acfit_app/build/windows/x64/runner/CMakeFiles/generate.stamp
if %errorlevel% neq 0 goto :cmEnd
:cmEnd
endlocal &amp; call :cmErrorLevel %errorlevel% &amp; goto :cmDone
:cmErrorLevel
exit /b %1
:cmDone
if %errorlevel% neq 0 goto :VCEnd</Command>
      <AdditionalInputs Condition="'$(Configuration)|$(Platform)'=='Debug|x64'">%(AdditionalInputs)</AdditionalInputs>
      <Outputs Condition="'$(Configuration)|$(Platform)'=='Debug|x64'">D:\PROGRAMMING\AC-FIT\acfit_app\build\windows\x64\runner\CMakeFiles\generate.stamp</Outputs>
      <LinkObjects Condition="'$(Configuration)|$(Platform)'=='Debug|x64'">false</LinkObjects>
      <Message Condition="'$(Configuration)|$(Platform)'=='Profile|x64'">Building Custom Rule D:/PROGRAMMING/AC-FIT/acfit_app/windows/runner/CMakeLists.txt</Message>
      <Command Condition="'$(Configuration)|$(Platform)'=='Profile|x64'">setlocal
"C:\Program Files\Microsoft Visual Studio\2022\Community\Common7\IDE\CommonExtensions\Microsoft\CMake\CMake\bin\cmake.exe" -SD:/PROGRAMMING/AC-FIT/acfit_app/windows -BD:/PROGRAMMING/AC-FIT/acfit_app/build/windows/x64 --check-stamp-file D:/PROGRAMMING/AC-FIT/acfit_app/build/windows/x64/runner/CMakeFiles/generate.stamp
if %errorlevel% neq 0 goto :cmEnd
:cmEnd
endlocal &amp; call :cmErrorLevel %errorlevel% &amp; goto :cmDone
:cmErrorLevel
exit /b %1
:cmDone
if %errorlevel% neq 0 goto :VCEnd</Command>
      <AdditionalInputs Condition="'$(Configuration)|$(Platform)'=='Profile|x64'">%(AdditionalInputs)</AdditionalInputs>
      <Outputs Condition="'$(Configuration)|$(Platform)'=='Profile|x64'">D:\PROGRAMMING\AC-FIT\acfit_app\build\windows\x64\runner\CMakeFiles\generate.stamp</Outputs>
      <LinkObjects Condition="'$(Configuration)|$(Platform)'=='Profile|x64'">false</LinkObjects>
      <Message Condition="'$(Configuration)|$(Platform)'=='Release|x64'">Building Custom Rule D:/PROGRAMMING/AC-FIT/acfit_app/windows/runner/CMakeLists.txt</Message>
      <Command Condition="'$(Configuration)|$(Platform)'=='Release|x64'">setlocal
"C:\Program Files\Microsoft Visual Studio\2022\Community\Common7\IDE\CommonExtensions\Microsoft\CMake\CMake\bin\cmake.exe" -SD:/PROGRAMMING/AC-FIT/acfit_app/windows -BD:/PROGRAMMING/AC-FIT/acfit_app/build/windows/x64 --check-stamp-file D:/PROGRAMMING/AC-FIT/acfit_app/build/windows/x64/runner/CMakeFiles/generate.stamp
if %errorlevel% neq 0 goto :cmEnd
:cmEnd
endlocal &amp; call :cmErrorLevel %errorlevel% &amp; goto :cmDone
:cmErrorLevel
exit /b %1
:cmDone
if %errorlevel% neq 0 goto :VCEnd</Command>
      <AdditionalInputs Condition="'$(Configuration)|$(Platform)'=='Release|x64'">%(AdditionalInputs)</AdditionalInputs>
      <Outputs Condition="'$(Configuration)|$(Platform)'=='Release|x64'">D:\PROGRAMMING\AC-FIT\acfit_app\build\windows\x64\runner\CMakeFiles\generate.stamp</Outputs>
      <LinkObjects Condition="'$(Configuration)|$(Platform)'=='Release|x64'">false</LinkObjects>
    </CustomBuild>
  </ItemGroup>
  <ItemGroup>
    <ClCompile Include="D:\PROGRAMMING\AC-FIT\acfit_app\windows\runner\flutter_window.cpp" />
    <ClCompile Include="D:\PROGRAMMING\AC-FIT\acfit_app\windows\runner\main.cpp" />
    <ClCompile Include="D:\PROGRAMMING\AC-FIT\acfit_app\windows\runner\utils.cpp" />
    <ClCompile Include="D:\PROGRAMMING\AC-FIT\acfit_app\windows\runner\win32_window.cpp" />
    <ClCompile Include="D:\PROGRAMMING\AC-FIT\acfit_app\windows\flutter\generated_plugin_registrant.cc" />
    <ResourceCompile Include="D:\PROGRAMMING\AC-FIT\acfit_app\windows\runner\Runner.rc" />
  </ItemGroup>
  <ItemGroup />
  <ItemGroup>
    <ProjectReference Include="D:\PROGRAMMING\AC-FIT\acfit_app\build\windows\x64\ZERO_CHECK.vcxproj">
      <Project>{FFCED161-E4F3-3C8F-9A21-C1EBE5EFE78A}</Project>
      <Name>ZERO_CHECK</Name>
      <ReferenceOutputAssembly>false</ReferenceOutputAssembly>
      <CopyToOutputDirectory>Never</CopyToOutputDirectory>
    </ProjectReference>
    <ProjectReference Include="D:\PROGRAMMING\AC-FIT\acfit_app\build\windows\x64\plugins\audioplayers_windows\audioplayers_windows_plugin.vcxproj">
      <Project>{D21F249C-54CA-31DE-82D3-593EC4DF94AB}</Project>
      <Name>audioplayers_windows_plugin</Name>
    </ProjectReference>
    <ProjectReference Include="D:\PROGRAMMING\AC-FIT\acfit_app\build\windows\x64\plugins\connectivity_plus\connectivity_plus_plugin.vcxproj">
      <Project>{D20E92F3-BAF1-379F-9FC1-EDA03941635E}</Project>
      <Name>connectivity_plus_plugin</Name>
    </ProjectReference>
    <ProjectReference Include="D:\PROGRAMMING\AC-FIT\acfit_app\build\windows\x64\plugins\file_selector_windows\file_selector_windows_plugin.vcxproj">
      <Project>{BFD446CA-DA9F-3EFD-89A4-2FF7A74B5EC9}</Project>
      <Name>file_selector_windows_plugin</Name>
    </ProjectReference>
    <ProjectReference Include="D:\PROGRAMMING\AC-FIT\acfit_app\build\windows\x64\flutter\flutter_assemble.vcxproj">
      <Project>{66CEF379-F235-3E6F-9C51-782657F6D12E}</Project>
      <Name>flutter_assemble</Name>
      <ReferenceOutputAssembly>false</ReferenceOutputAssembly>
      <CopyToOutputDirectory>Never</CopyToOutputDirectory>
    </ProjectReference>
    <ProjectReference Include="D:\PROGRAMMING\AC-FIT\acfit_app\build\windows\x64\plugins\flutter_secure_storage_windows\flutter_secure_storage_windows_plugin.vcxproj">
      <Project>{F9799DD6-81D6-3A33-8B2E-EB75CF6D4BAD}</Project>
      <Name>flutter_secure_storage_windows_plugin</Name>
    </ProjectReference>
    <ProjectReference Include="D:\PROGRAMMING\AC-FIT\acfit_app\build\windows\x64\plugins\flutter_timezone\flutter_timezone_plugin.vcxproj">
      <Project>{71EC089D-2CD1-3EEB-8415-49F9BCECD2DC}</Project>
      <Name>flutter_timezone_plugin</Name>
    </ProjectReference>
    <ProjectReference Include="D:\PROGRAMMING\AC-FIT\acfit_app\build\windows\x64\flutter\flutter_wrapper_app.vcxproj">
      <Project>{D7E7ABFE-F6E4-37D6-B697-B9C5A450954A}</Project>
      <Name>flutter_wrapper_app</Name>
    </ProjectReference>
    <ProjectReference Include="D:\PROGRAMMING\AC-FIT\acfit_app\build\windows\x64\plugins\url_launcher_windows\url_launcher_windows_plugin.vcxproj">
      <Project>{B77A7839-2464-3FC3-9B6A-F3DD6E854AD2}</Project>
      <Name>url_launcher_windows_plugin</Name>
    </ProjectReference>
  </ItemGroup>
  <Import Project="$(VCTargetsPath)\Microsoft.Cpp.targets" />
  <ImportGroup Label="ExtensionTargets">
  </ImportGroup>
</Project>