1<?xml version="1.0" encoding="utf-8"?>
2<manifest xmlns:android="http://schemas.android.com/apk/res/android"
3    package="com.acfit.app"
4    android:versionCode="1"
5    android:versionName="0.1.0" >
6
7    <uses-sdk
8        android:minSdkVersion="21"
9        android:targetSdkVersion="35" />
10    <!-- Internet permission for API access -->
11    <uses-permission android:name="android.permission.INTERNET" />
11-->D:\PROGRAMMING\AC-FIT\acfit_app\android\app\src\main\AndroidManifest.xml:3:5-67
11-->D:\PROGRAMMING\AC-FIT\acfit_app\android\app\src\main\AndroidManifest.xml:3:22-64
12    <!--
13         Required to query activities that can process text, see:
14         https://developer.android.com/training/package-visibility and
15         https://developer.android.com/reference/android/content/Intent#ACTION_PROCESS_TEXT.
16
17         In particular, this is used by the Flutter engine in io.flutter.plugin.text.ProcessTextPlugin.
18    -->
19    <queries>
19-->D:\PROGRAMMING\AC-FIT\acfit_app\android\app\src\main\AndroidManifest.xml:43:5-60:15
20        <intent>
20-->D:\PROGRAMMING\AC-FIT\acfit_app\android\app\src\main\AndroidManifest.xml:44:9-47:18
21            <action android:name="android.intent.action.PROCESS_TEXT" />
21-->D:\PROGRAMMING\AC-FIT\acfit_app\android\app\src\main\AndroidManifest.xml:45:13-72
21-->D:\PROGRAMMING\AC-FIT\acfit_app\android\app\src\main\AndroidManifest.xml:45:21-70
22
23            <data android:mimeType="text/plain" />
23-->D:\PROGRAMMING\AC-FIT\acfit_app\android\app\src\main\AndroidManifest.xml:46:13-50
23-->D:\PROGRAMMING\AC-FIT\acfit_app\android\app\src\main\AndroidManifest.xml:46:19-48
24        </intent>
25        <!-- Required for url_launcher to work with https URLs -->
26        <intent>
26-->D:\PROGRAMMING\AC-FIT\acfit_app\android\app\src\main\AndroidManifest.xml:49:9-53:18
27            <action android:name="android.intent.action.VIEW" />
27-->D:\PROGRAMMING\AC-FIT\acfit_app\android\app\src\main\AndroidManifest.xml:50:13-65
27-->D:\PROGRAMMING\AC-FIT\acfit_app\android\app\src\main\AndroidManifest.xml:50:21-62
28
29            <category android:name="android.intent.category.BROWSABLE" />
29-->D:\PROGRAMMING\AC-FIT\acfit_app\android\app\src\main\AndroidManifest.xml:51:13-74
29-->D:\PROGRAMMING\AC-FIT\acfit_app\android\app\src\main\AndroidManifest.xml:51:23-71
30
31            <data android:scheme="https" />
31-->D:\PROGRAMMING\AC-FIT\acfit_app\android\app\src\main\AndroidManifest.xml:46:13-50
31-->D:\PROGRAMMING\AC-FIT\acfit_app\android\app\src\main\AndroidManifest.xml:52:19-41
32        </intent>
33        <!-- Required for url_launcher to work with http URLs -->
34        <intent>
34-->D:\PROGRAMMING\AC-FIT\acfit_app\android\app\src\main\AndroidManifest.xml:55:9-59:18
35            <action android:name="android.intent.action.VIEW" />
35-->D:\PROGRAMMING\AC-FIT\acfit_app\android\app\src\main\AndroidManifest.xml:50:13-65
35-->D:\PROGRAMMING\AC-FIT\acfit_app\android\app\src\main\AndroidManifest.xml:50:21-62
36
37            <category android:name="android.intent.category.BROWSABLE" />
37-->D:\PROGRAMMING\AC-FIT\acfit_app\android\app\src\main\AndroidManifest.xml:51:13-74
37-->D:\PROGRAMMING\AC-FIT\acfit_app\android\app\src\main\AndroidManifest.xml:51:23-71
38
39            <data android:scheme="http" />
39-->D:\PROGRAMMING\AC-FIT\acfit_app\android\app\src\main\AndroidManifest.xml:46:13-50
39-->D:\PROGRAMMING\AC-FIT\acfit_app\android\app\src\main\AndroidManifest.xml:52:19-41
40        </intent>
41    </queries>
42
43    <uses-permission android:name="android.permission.VIBRATE" />
43-->[:flutter_local_notifications] D:\PROGRAMMING\AC-FIT\acfit_app\build\flutter_local_notifications\intermediates\merged_manifest\release\processReleaseManifest\AndroidManifest.xml:7:5-66
43-->[:flutter_local_notifications] D:\PROGRAMMING\AC-FIT\acfit_app\build\flutter_local_notifications\intermediates\merged_manifest\release\processReleaseManifest\AndroidManifest.xml:7:22-63
44    <uses-permission android:name="android.permission.POST_NOTIFICATIONS" />
44-->[:flutter_local_notifications] D:\PROGRAMMING\AC-FIT\acfit_app\build\flutter_local_notifications\intermediates\merged_manifest\release\processReleaseManifest\AndroidManifest.xml:8:5-77
44-->[:flutter_local_notifications] D:\PROGRAMMING\AC-FIT\acfit_app\build\flutter_local_notifications\intermediates\merged_manifest\release\processReleaseManifest\AndroidManifest.xml:8:22-74
45    <uses-permission android:name="android.permission.ACCESS_NETWORK_STATE" />
45-->[:connectivity_plus] D:\PROGRAMMING\AC-FIT\acfit_app\build\connectivity_plus\intermediates\merged_manifest\release\processReleaseManifest\AndroidManifest.xml:7:5-79
45-->[:connectivity_plus] D:\PROGRAMMING\AC-FIT\acfit_app\build\connectivity_plus\intermediates\merged_manifest\release\processReleaseManifest\AndroidManifest.xml:7:22-76
46    <uses-permission android:name="android.permission.WAKE_LOCK" />
46-->[androidx.work:work-runtime:2.8.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\8975063235ea0ba114f3bb0240765838\transformed\work-runtime-2.8.1\AndroidManifest.xml:23:5-68
46-->[androidx.work:work-runtime:2.8.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\8975063235ea0ba114f3bb0240765838\transformed\work-runtime-2.8.1\AndroidManifest.xml:23:22-65
47    <uses-permission android:name="android.permission.RECEIVE_BOOT_COMPLETED" />
47-->[androidx.work:work-runtime:2.8.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\8975063235ea0ba114f3bb0240765838\transformed\work-runtime-2.8.1\AndroidManifest.xml:25:5-81
47-->[androidx.work:work-runtime:2.8.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\8975063235ea0ba114f3bb0240765838\transformed\work-runtime-2.8.1\AndroidManifest.xml:25:22-78
48    <uses-permission android:name="android.permission.FOREGROUND_SERVICE" />
48-->[androidx.work:work-runtime:2.8.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\8975063235ea0ba114f3bb0240765838\transformed\work-runtime-2.8.1\AndroidManifest.xml:26:5-77
48-->[androidx.work:work-runtime:2.8.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\8975063235ea0ba114f3bb0240765838\transformed\work-runtime-2.8.1\AndroidManifest.xml:26:22-74
49
50    <permission
50-->[androidx.core:core:1.13.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\deb838f5c4813d0c441214e1ddf497cc\transformed\core-1.13.1\AndroidManifest.xml:22:5-24:47
51        android:name="com.acfit.app.DYNAMIC_RECEIVER_NOT_EXPORTED_PERMISSION"
51-->[androidx.core:core:1.13.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\deb838f5c4813d0c441214e1ddf497cc\transformed\core-1.13.1\AndroidManifest.xml:23:9-81
52        android:protectionLevel="signature" />
52-->[androidx.core:core:1.13.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\deb838f5c4813d0c441214e1ddf497cc\transformed\core-1.13.1\AndroidManifest.xml:24:9-44
53
54    <uses-permission android:name="com.acfit.app.DYNAMIC_RECEIVER_NOT_EXPORTED_PERMISSION" />
54-->[androidx.core:core:1.13.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\deb838f5c4813d0c441214e1ddf497cc\transformed\core-1.13.1\AndroidManifest.xml:26:5-97
54-->[androidx.core:core:1.13.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\deb838f5c4813d0c441214e1ddf497cc\transformed\core-1.13.1\AndroidManifest.xml:26:22-94
55
56    <application
57        android:name="android.app.Application"
57-->D:\PROGRAMMING\AC-FIT\acfit_app\android\app\src\main\AndroidManifest.xml:6:9-42
58        android:appComponentFactory="androidx.core.app.CoreComponentFactory"
58-->[androidx.core:core:1.13.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\deb838f5c4813d0c441214e1ddf497cc\transformed\core-1.13.1\AndroidManifest.xml:28:18-86
59        android:enableOnBackInvokedCallback="true"
59-->D:\PROGRAMMING\AC-FIT\acfit_app\android\app\src\main\AndroidManifest.xml:9:9-51
60        android:extractNativeLibs="true"
61        android:icon="@mipmap/ic_launcher"
61-->D:\PROGRAMMING\AC-FIT\acfit_app\android\app\src\main\AndroidManifest.xml:7:9-43
62        android:label="AC Fit App"
62-->D:\PROGRAMMING\AC-FIT\acfit_app\android\app\src\main\AndroidManifest.xml:5:9-35
63        android:usesCleartextTraffic="true" >
63-->D:\PROGRAMMING\AC-FIT\acfit_app\android\app\src\main\AndroidManifest.xml:8:9-44
64        <activity
64-->D:\PROGRAMMING\AC-FIT\acfit_app\android\app\src\main\AndroidManifest.xml:10:9-31:20
65            android:name="com.example.acfit_app.MainActivity"
65-->D:\PROGRAMMING\AC-FIT\acfit_app\android\app\src\main\AndroidManifest.xml:11:13-41
66            android:configChanges="orientation|keyboardHidden|keyboard|screenSize|smallestScreenSize|locale|layoutDirection|fontScale|screenLayout|density|uiMode"
66-->D:\PROGRAMMING\AC-FIT\acfit_app\android\app\src\main\AndroidManifest.xml:16:13-163
67            android:exported="true"
67-->D:\PROGRAMMING\AC-FIT\acfit_app\android\app\src\main\AndroidManifest.xml:12:13-36
68            android:hardwareAccelerated="true"
68-->D:\PROGRAMMING\AC-FIT\acfit_app\android\app\src\main\AndroidManifest.xml:17:13-47
69            android:launchMode="singleTop"
69-->D:\PROGRAMMING\AC-FIT\acfit_app\android\app\src\main\AndroidManifest.xml:13:13-43
70            android:taskAffinity=""
70-->D:\PROGRAMMING\AC-FIT\acfit_app\android\app\src\main\AndroidManifest.xml:14:13-36
71            android:theme="@style/LaunchTheme"
71-->D:\PROGRAMMING\AC-FIT\acfit_app\android\app\src\main\AndroidManifest.xml:15:13-47
72            android:windowSoftInputMode="adjustResize" >
72-->D:\PROGRAMMING\AC-FIT\acfit_app\android\app\src\main\AndroidManifest.xml:18:13-55
73
74            <!--
75                 Specifies an Android theme to apply to this Activity as soon as
76                 the Android process has started. This theme is visible to the user
77                 while the Flutter UI initializes. After that, this theme continues
78                 to determine the Window background behind the Flutter UI.
79            -->
80            <meta-data
80-->D:\PROGRAMMING\AC-FIT\acfit_app\android\app\src\main\AndroidManifest.xml:23:13-26:17
81                android:name="io.flutter.embedding.android.NormalTheme"
81-->D:\PROGRAMMING\AC-FIT\acfit_app\android\app\src\main\AndroidManifest.xml:24:15-70
82                android:resource="@style/NormalTheme" />
82-->D:\PROGRAMMING\AC-FIT\acfit_app\android\app\src\main\AndroidManifest.xml:25:15-52
83
84            <intent-filter>
84-->D:\PROGRAMMING\AC-FIT\acfit_app\android\app\src\main\AndroidManifest.xml:27:13-30:29
85                <action android:name="android.intent.action.MAIN" />
85-->D:\PROGRAMMING\AC-FIT\acfit_app\android\app\src\main\AndroidManifest.xml:28:17-68
85-->D:\PROGRAMMING\AC-FIT\acfit_app\android\app\src\main\AndroidManifest.xml:28:25-66
86
87                <category android:name="android.intent.category.LAUNCHER" />
87-->D:\PROGRAMMING\AC-FIT\acfit_app\android\app\src\main\AndroidManifest.xml:29:17-76
87-->D:\PROGRAMMING\AC-FIT\acfit_app\android\app\src\main\AndroidManifest.xml:29:27-74
88            </intent-filter>
89        </activity>
90        <!--
91             Don't delete the meta-data below.
92             This is used by the Flutter tool to generate GeneratedPluginRegistrant.java
93        -->
94        <meta-data
94-->D:\PROGRAMMING\AC-FIT\acfit_app\android\app\src\main\AndroidManifest.xml:34:9-36:33
95            android:name="flutterEmbedding"
95-->D:\PROGRAMMING\AC-FIT\acfit_app\android\app\src\main\AndroidManifest.xml:35:13-44
96            android:value="2" />
96-->D:\PROGRAMMING\AC-FIT\acfit_app\android\app\src\main\AndroidManifest.xml:36:13-30
97
98        <provider
98-->[:image_picker_android] D:\PROGRAMMING\AC-FIT\acfit_app\build\image_picker_android\intermediates\merged_manifest\release\processReleaseManifest\AndroidManifest.xml:9:9-17:20
99            android:name="io.flutter.plugins.imagepicker.ImagePickerFileProvider"
99-->[:image_picker_android] D:\PROGRAMMING\AC-FIT\acfit_app\build\image_picker_android\intermediates\merged_manifest\release\processReleaseManifest\AndroidManifest.xml:10:13-82
100            android:authorities="com.acfit.app.flutter.image_provider"
100-->[:image_picker_android] D:\PROGRAMMING\AC-FIT\acfit_app\build\image_picker_android\intermediates\merged_manifest\release\processReleaseManifest\AndroidManifest.xml:11:13-74
101            android:exported="false"
101-->[:image_picker_android] D:\PROGRAMMING\AC-FIT\acfit_app\build\image_picker_android\intermediates\merged_manifest\release\processReleaseManifest\AndroidManifest.xml:12:13-37
102            android:grantUriPermissions="true" >
102-->[:image_picker_android] D:\PROGRAMMING\AC-FIT\acfit_app\build\image_picker_android\intermediates\merged_manifest\release\processReleaseManifest\AndroidManifest.xml:13:13-47
103            <meta-data
103-->[:image_picker_android] D:\PROGRAMMING\AC-FIT\acfit_app\build\image_picker_android\intermediates\merged_manifest\release\processReleaseManifest\AndroidManifest.xml:14:13-16:75
104                android:name="android.support.FILE_PROVIDER_PATHS"
104-->[:image_picker_android] D:\PROGRAMMING\AC-FIT\acfit_app\build\image_picker_android\intermediates\merged_manifest\release\processReleaseManifest\AndroidManifest.xml:15:17-67
105                android:resource="@xml/flutter_image_picker_file_paths" />
105-->[:image_picker_android] D:\PROGRAMMING\AC-FIT\acfit_app\build\image_picker_android\intermediates\merged_manifest\release\processReleaseManifest\AndroidManifest.xml:16:17-72
106        </provider> <!-- Trigger Google Play services to install the backported photo picker module. -->
107        <service
107-->[:image_picker_android] D:\PROGRAMMING\AC-FIT\acfit_app\build\image_picker_android\intermediates\merged_manifest\release\processReleaseManifest\AndroidManifest.xml:19:9-31:19
108            android:name="com.google.android.gms.metadata.ModuleDependencies"
108-->[:image_picker_android] D:\PROGRAMMING\AC-FIT\acfit_app\build\image_picker_android\intermediates\merged_manifest\release\processReleaseManifest\AndroidManifest.xml:20:13-78
109            android:enabled="false"
109-->[:image_picker_android] D:\PROGRAMMING\AC-FIT\acfit_app\build\image_picker_android\intermediates\merged_manifest\release\processReleaseManifest\AndroidManifest.xml:21:13-36
110            android:exported="false" >
110-->[:image_picker_android] D:\PROGRAMMING\AC-FIT\acfit_app\build\image_picker_android\intermediates\merged_manifest\release\processReleaseManifest\AndroidManifest.xml:22:13-37
111            <intent-filter>
111-->[:image_picker_android] D:\PROGRAMMING\AC-FIT\acfit_app\build\image_picker_android\intermediates\merged_manifest\release\processReleaseManifest\AndroidManifest.xml:24:13-26:29
112                <action android:name="com.google.android.gms.metadata.MODULE_DEPENDENCIES" />
112-->[:image_picker_android] D:\PROGRAMMING\AC-FIT\acfit_app\build\image_picker_android\intermediates\merged_manifest\release\processReleaseManifest\AndroidManifest.xml:25:17-94
112-->[:image_picker_android] D:\PROGRAMMING\AC-FIT\acfit_app\build\image_picker_android\intermediates\merged_manifest\release\processReleaseManifest\AndroidManifest.xml:25:25-91
113            </intent-filter>
114
115            <meta-data
115-->[:image_picker_android] D:\PROGRAMMING\AC-FIT\acfit_app\build\image_picker_android\intermediates\merged_manifest\release\processReleaseManifest\AndroidManifest.xml:28:13-30:36
116                android:name="photopicker_activity:0:required"
116-->[:image_picker_android] D:\PROGRAMMING\AC-FIT\acfit_app\build\image_picker_android\intermediates\merged_manifest\release\processReleaseManifest\AndroidManifest.xml:29:17-63
117                android:value="" />
117-->[:image_picker_android] D:\PROGRAMMING\AC-FIT\acfit_app\build\image_picker_android\intermediates\merged_manifest\release\processReleaseManifest\AndroidManifest.xml:30:17-33
118        </service>
119
120        <activity
120-->[:url_launcher_android] D:\PROGRAMMING\AC-FIT\acfit_app\build\url_launcher_android\intermediates\merged_manifest\release\processReleaseManifest\AndroidManifest.xml:8:9-11:74
121            android:name="io.flutter.plugins.urllauncher.WebViewActivity"
121-->[:url_launcher_android] D:\PROGRAMMING\AC-FIT\acfit_app\build\url_launcher_android\intermediates\merged_manifest\release\processReleaseManifest\AndroidManifest.xml:9:13-74
122            android:exported="false"
122-->[:url_launcher_android] D:\PROGRAMMING\AC-FIT\acfit_app\build\url_launcher_android\intermediates\merged_manifest\release\processReleaseManifest\AndroidManifest.xml:10:13-37
123            android:theme="@android:style/Theme.NoTitleBar.Fullscreen" />
123-->[:url_launcher_android] D:\PROGRAMMING\AC-FIT\acfit_app\build\url_launcher_android\intermediates\merged_manifest\release\processReleaseManifest\AndroidManifest.xml:11:13-71
124
125        <provider
125-->[androidx.work:work-runtime:2.8.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\8975063235ea0ba114f3bb0240765838\transformed\work-runtime-2.8.1\AndroidManifest.xml:29:9-37:20
126            android:name="androidx.startup.InitializationProvider"
126-->[androidx.work:work-runtime:2.8.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\8975063235ea0ba114f3bb0240765838\transformed\work-runtime-2.8.1\AndroidManifest.xml:30:13-67
127            android:authorities="com.acfit.app.androidx-startup"
127-->[androidx.work:work-runtime:2.8.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\8975063235ea0ba114f3bb0240765838\transformed\work-runtime-2.8.1\AndroidManifest.xml:31:13-68
128            android:exported="false" >
128-->[androidx.work:work-runtime:2.8.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\8975063235ea0ba114f3bb0240765838\transformed\work-runtime-2.8.1\AndroidManifest.xml:32:13-37
129            <meta-data
129-->[androidx.work:work-runtime:2.8.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\8975063235ea0ba114f3bb0240765838\transformed\work-runtime-2.8.1\AndroidManifest.xml:34:13-36:52
130                android:name="androidx.work.WorkManagerInitializer"
130-->[androidx.work:work-runtime:2.8.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\8975063235ea0ba114f3bb0240765838\transformed\work-runtime-2.8.1\AndroidManifest.xml:35:17-68
131                android:value="androidx.startup" />
131-->[androidx.work:work-runtime:2.8.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\8975063235ea0ba114f3bb0240765838\transformed\work-runtime-2.8.1\AndroidManifest.xml:36:17-49
132            <meta-data
132-->[androidx.lifecycle:lifecycle-process:2.7.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\e920d985392df03e5b02e06877a52d21\transformed\jetified-lifecycle-process-2.7.0\AndroidManifest.xml:29:13-31:52
133                android:name="androidx.lifecycle.ProcessLifecycleInitializer"
133-->[androidx.lifecycle:lifecycle-process:2.7.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\e920d985392df03e5b02e06877a52d21\transformed\jetified-lifecycle-process-2.7.0\AndroidManifest.xml:30:17-78
134                android:value="androidx.startup" />
134-->[androidx.lifecycle:lifecycle-process:2.7.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\e920d985392df03e5b02e06877a52d21\transformed\jetified-lifecycle-process-2.7.0\AndroidManifest.xml:31:17-49
135            <meta-data
135-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\eabc1325411a824ae74b9b450245750e\transformed\jetified-profileinstaller-1.3.1\AndroidManifest.xml:29:13-31:52
136                android:name="androidx.profileinstaller.ProfileInstallerInitializer"
136-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\eabc1325411a824ae74b9b450245750e\transformed\jetified-profileinstaller-1.3.1\AndroidManifest.xml:30:17-85
137                android:value="androidx.startup" />
137-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\eabc1325411a824ae74b9b450245750e\transformed\jetified-profileinstaller-1.3.1\AndroidManifest.xml:31:17-49
138        </provider>
139
140        <service
140-->[androidx.work:work-runtime:2.8.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\8975063235ea0ba114f3bb0240765838\transformed\work-runtime-2.8.1\AndroidManifest.xml:39:9-45:35
141            android:name="androidx.work.impl.background.systemalarm.SystemAlarmService"
141-->[androidx.work:work-runtime:2.8.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\8975063235ea0ba114f3bb0240765838\transformed\work-runtime-2.8.1\AndroidManifest.xml:40:13-88
142            android:directBootAware="false"
142-->[androidx.work:work-runtime:2.8.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\8975063235ea0ba114f3bb0240765838\transformed\work-runtime-2.8.1\AndroidManifest.xml:41:13-44
143            android:enabled="@bool/enable_system_alarm_service_default"
143-->[androidx.work:work-runtime:2.8.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\8975063235ea0ba114f3bb0240765838\transformed\work-runtime-2.8.1\AndroidManifest.xml:42:13-72
144            android:exported="false" />
144-->[androidx.work:work-runtime:2.8.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\8975063235ea0ba114f3bb0240765838\transformed\work-runtime-2.8.1\AndroidManifest.xml:43:13-37
145        <service
145-->[androidx.work:work-runtime:2.8.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\8975063235ea0ba114f3bb0240765838\transformed\work-runtime-2.8.1\AndroidManifest.xml:46:9-52:35
146            android:name="androidx.work.impl.background.systemjob.SystemJobService"
146-->[androidx.work:work-runtime:2.8.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\8975063235ea0ba114f3bb0240765838\transformed\work-runtime-2.8.1\AndroidManifest.xml:47:13-84
147            android:directBootAware="false"
147-->[androidx.work:work-runtime:2.8.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\8975063235ea0ba114f3bb0240765838\transformed\work-runtime-2.8.1\AndroidManifest.xml:48:13-44
148            android:enabled="@bool/enable_system_job_service_default"
148-->[androidx.work:work-runtime:2.8.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\8975063235ea0ba114f3bb0240765838\transformed\work-runtime-2.8.1\AndroidManifest.xml:49:13-70
149            android:exported="true"
149-->[androidx.work:work-runtime:2.8.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\8975063235ea0ba114f3bb0240765838\transformed\work-runtime-2.8.1\AndroidManifest.xml:50:13-36
150            android:permission="android.permission.BIND_JOB_SERVICE" />
150-->[androidx.work:work-runtime:2.8.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\8975063235ea0ba114f3bb0240765838\transformed\work-runtime-2.8.1\AndroidManifest.xml:51:13-69
151        <service
151-->[androidx.work:work-runtime:2.8.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\8975063235ea0ba114f3bb0240765838\transformed\work-runtime-2.8.1\AndroidManifest.xml:53:9-59:35
152            android:name="androidx.work.impl.foreground.SystemForegroundService"
152-->[androidx.work:work-runtime:2.8.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\8975063235ea0ba114f3bb0240765838\transformed\work-runtime-2.8.1\AndroidManifest.xml:54:13-81
153            android:directBootAware="false"
153-->[androidx.work:work-runtime:2.8.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\8975063235ea0ba114f3bb0240765838\transformed\work-runtime-2.8.1\AndroidManifest.xml:55:13-44
154            android:enabled="@bool/enable_system_foreground_service_default"
154-->[androidx.work:work-runtime:2.8.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\8975063235ea0ba114f3bb0240765838\transformed\work-runtime-2.8.1\AndroidManifest.xml:56:13-77
155            android:exported="false" />
155-->[androidx.work:work-runtime:2.8.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\8975063235ea0ba114f3bb0240765838\transformed\work-runtime-2.8.1\AndroidManifest.xml:57:13-37
156
157        <receiver
157-->[androidx.work:work-runtime:2.8.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\8975063235ea0ba114f3bb0240765838\transformed\work-runtime-2.8.1\AndroidManifest.xml:61:9-66:35
158            android:name="androidx.work.impl.utils.ForceStopRunnable$BroadcastReceiver"
158-->[androidx.work:work-runtime:2.8.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\8975063235ea0ba114f3bb0240765838\transformed\work-runtime-2.8.1\AndroidManifest.xml:62:13-88
159            android:directBootAware="false"
159-->[androidx.work:work-runtime:2.8.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\8975063235ea0ba114f3bb0240765838\transformed\work-runtime-2.8.1\AndroidManifest.xml:63:13-44
160            android:enabled="true"
160-->[androidx.work:work-runtime:2.8.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\8975063235ea0ba114f3bb0240765838\transformed\work-runtime-2.8.1\AndroidManifest.xml:64:13-35
161            android:exported="false" />
161-->[androidx.work:work-runtime:2.8.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\8975063235ea0ba114f3bb0240765838\transformed\work-runtime-2.8.1\AndroidManifest.xml:65:13-37
162        <receiver
162-->[androidx.work:work-runtime:2.8.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\8975063235ea0ba114f3bb0240765838\transformed\work-runtime-2.8.1\AndroidManifest.xml:67:9-77:20
163            android:name="androidx.work.impl.background.systemalarm.ConstraintProxy$BatteryChargingProxy"
163-->[androidx.work:work-runtime:2.8.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\8975063235ea0ba114f3bb0240765838\transformed\work-runtime-2.8.1\AndroidManifest.xml:68:13-106
164            android:directBootAware="false"
164-->[androidx.work:work-runtime:2.8.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\8975063235ea0ba114f3bb0240765838\transformed\work-runtime-2.8.1\AndroidManifest.xml:69:13-44
165            android:enabled="false"
165-->[androidx.work:work-runtime:2.8.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\8975063235ea0ba114f3bb0240765838\transformed\work-runtime-2.8.1\AndroidManifest.xml:70:13-36
166            android:exported="false" >
166-->[androidx.work:work-runtime:2.8.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\8975063235ea0ba114f3bb0240765838\transformed\work-runtime-2.8.1\AndroidManifest.xml:71:13-37
167            <intent-filter>
167-->[androidx.work:work-runtime:2.8.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\8975063235ea0ba114f3bb0240765838\transformed\work-runtime-2.8.1\AndroidManifest.xml:73:13-76:29
168                <action android:name="android.intent.action.ACTION_POWER_CONNECTED" />
168-->[androidx.work:work-runtime:2.8.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\8975063235ea0ba114f3bb0240765838\transformed\work-runtime-2.8.1\AndroidManifest.xml:74:17-87
168-->[androidx.work:work-runtime:2.8.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\8975063235ea0ba114f3bb0240765838\transformed\work-runtime-2.8.1\AndroidManifest.xml:74:25-84
169                <action android:name="android.intent.action.ACTION_POWER_DISCONNECTED" />
169-->[androidx.work:work-runtime:2.8.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\8975063235ea0ba114f3bb0240765838\transformed\work-runtime-2.8.1\AndroidManifest.xml:75:17-90
169-->[androidx.work:work-runtime:2.8.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\8975063235ea0ba114f3bb0240765838\transformed\work-runtime-2.8.1\AndroidManifest.xml:75:25-87
170            </intent-filter>
171        </receiver>
172        <receiver
172-->[androidx.work:work-runtime:2.8.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\8975063235ea0ba114f3bb0240765838\transformed\work-runtime-2.8.1\AndroidManifest.xml:78:9-88:20
173            android:name="androidx.work.impl.background.systemalarm.ConstraintProxy$BatteryNotLowProxy"
173-->[androidx.work:work-runtime:2.8.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\8975063235ea0ba114f3bb0240765838\transformed\work-runtime-2.8.1\AndroidManifest.xml:79:13-104
174            android:directBootAware="false"
174-->[androidx.work:work-runtime:2.8.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\8975063235ea0ba114f3bb0240765838\transformed\work-runtime-2.8.1\AndroidManifest.xml:80:13-44
175            android:enabled="false"
175-->[androidx.work:work-runtime:2.8.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\8975063235ea0ba114f3bb0240765838\transformed\work-runtime-2.8.1\AndroidManifest.xml:81:13-36
176            android:exported="false" >
176-->[androidx.work:work-runtime:2.8.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\8975063235ea0ba114f3bb0240765838\transformed\work-runtime-2.8.1\AndroidManifest.xml:82:13-37
177            <intent-filter>
177-->[androidx.work:work-runtime:2.8.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\8975063235ea0ba114f3bb0240765838\transformed\work-runtime-2.8.1\AndroidManifest.xml:84:13-87:29
178                <action android:name="android.intent.action.BATTERY_OKAY" />
178-->[androidx.work:work-runtime:2.8.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\8975063235ea0ba114f3bb0240765838\transformed\work-runtime-2.8.1\AndroidManifest.xml:85:17-77
178-->[androidx.work:work-runtime:2.8.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\8975063235ea0ba114f3bb0240765838\transformed\work-runtime-2.8.1\AndroidManifest.xml:85:25-74
179                <action android:name="android.intent.action.BATTERY_LOW" />
179-->[androidx.work:work-runtime:2.8.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\8975063235ea0ba114f3bb0240765838\transformed\work-runtime-2.8.1\AndroidManifest.xml:86:17-76
179-->[androidx.work:work-runtime:2.8.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\8975063235ea0ba114f3bb0240765838\transformed\work-runtime-2.8.1\AndroidManifest.xml:86:25-73
180            </intent-filter>
181        </receiver>
182        <receiver
182-->[androidx.work:work-runtime:2.8.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\8975063235ea0ba114f3bb0240765838\transformed\work-runtime-2.8.1\AndroidManifest.xml:89:9-99:20
183            android:name="androidx.work.impl.background.systemalarm.ConstraintProxy$StorageNotLowProxy"
183-->[androidx.work:work-runtime:2.8.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\8975063235ea0ba114f3bb0240765838\transformed\work-runtime-2.8.1\AndroidManifest.xml:90:13-104
184            android:directBootAware="false"
184-->[androidx.work:work-runtime:2.8.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\8975063235ea0ba114f3bb0240765838\transformed\work-runtime-2.8.1\AndroidManifest.xml:91:13-44
185            android:enabled="false"
185-->[androidx.work:work-runtime:2.8.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\8975063235ea0ba114f3bb0240765838\transformed\work-runtime-2.8.1\AndroidManifest.xml:92:13-36
186            android:exported="false" >
186-->[androidx.work:work-runtime:2.8.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\8975063235ea0ba114f3bb0240765838\transformed\work-runtime-2.8.1\AndroidManifest.xml:93:13-37
187            <intent-filter>
187-->[androidx.work:work-runtime:2.8.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\8975063235ea0ba114f3bb0240765838\transformed\work-runtime-2.8.1\AndroidManifest.xml:95:13-98:29
188                <action android:name="android.intent.action.DEVICE_STORAGE_LOW" />
188-->[androidx.work:work-runtime:2.8.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\8975063235ea0ba114f3bb0240765838\transformed\work-runtime-2.8.1\AndroidManifest.xml:96:17-83
188-->[androidx.work:work-runtime:2.8.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\8975063235ea0ba114f3bb0240765838\transformed\work-runtime-2.8.1\AndroidManifest.xml:96:25-80
189                <action android:name="android.intent.action.DEVICE_STORAGE_OK" />
189-->[androidx.work:work-runtime:2.8.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\8975063235ea0ba114f3bb0240765838\transformed\work-runtime-2.8.1\AndroidManifest.xml:97:17-82
189-->[androidx.work:work-runtime:2.8.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\8975063235ea0ba114f3bb0240765838\transformed\work-runtime-2.8.1\AndroidManifest.xml:97:25-79
190            </intent-filter>
191        </receiver>
192        <receiver
192-->[androidx.work:work-runtime:2.8.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\8975063235ea0ba114f3bb0240765838\transformed\work-runtime-2.8.1\AndroidManifest.xml:100:9-109:20
193            android:name="androidx.work.impl.background.systemalarm.ConstraintProxy$NetworkStateProxy"
193-->[androidx.work:work-runtime:2.8.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\8975063235ea0ba114f3bb0240765838\transformed\work-runtime-2.8.1\AndroidManifest.xml:101:13-103
194            android:directBootAware="false"
194-->[androidx.work:work-runtime:2.8.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\8975063235ea0ba114f3bb0240765838\transformed\work-runtime-2.8.1\AndroidManifest.xml:102:13-44
195            android:enabled="false"
195-->[androidx.work:work-runtime:2.8.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\8975063235ea0ba114f3bb0240765838\transformed\work-runtime-2.8.1\AndroidManifest.xml:103:13-36
196            android:exported="false" >
196-->[androidx.work:work-runtime:2.8.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\8975063235ea0ba114f3bb0240765838\transformed\work-runtime-2.8.1\AndroidManifest.xml:104:13-37
197            <intent-filter>
197-->[androidx.work:work-runtime:2.8.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\8975063235ea0ba114f3bb0240765838\transformed\work-runtime-2.8.1\AndroidManifest.xml:106:13-108:29
198                <action android:name="android.net.conn.CONNECTIVITY_CHANGE" />
198-->[androidx.work:work-runtime:2.8.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\8975063235ea0ba114f3bb0240765838\transformed\work-runtime-2.8.1\AndroidManifest.xml:107:17-79
198-->[androidx.work:work-runtime:2.8.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\8975063235ea0ba114f3bb0240765838\transformed\work-runtime-2.8.1\AndroidManifest.xml:107:25-76
199            </intent-filter>
200        </receiver>
201        <receiver
201-->[androidx.work:work-runtime:2.8.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\8975063235ea0ba114f3bb0240765838\transformed\work-runtime-2.8.1\AndroidManifest.xml:110:9-121:20
202            android:name="androidx.work.impl.background.systemalarm.RescheduleReceiver"
202-->[androidx.work:work-runtime:2.8.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\8975063235ea0ba114f3bb0240765838\transformed\work-runtime-2.8.1\AndroidManifest.xml:111:13-88
203            android:directBootAware="false"
203-->[androidx.work:work-runtime:2.8.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\8975063235ea0ba114f3bb0240765838\transformed\work-runtime-2.8.1\AndroidManifest.xml:112:13-44
204            android:enabled="false"
204-->[androidx.work:work-runtime:2.8.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\8975063235ea0ba114f3bb0240765838\transformed\work-runtime-2.8.1\AndroidManifest.xml:113:13-36
205            android:exported="false" >
205-->[androidx.work:work-runtime:2.8.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\8975063235ea0ba114f3bb0240765838\transformed\work-runtime-2.8.1\AndroidManifest.xml:114:13-37
206            <intent-filter>
206-->[androidx.work:work-runtime:2.8.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\8975063235ea0ba114f3bb0240765838\transformed\work-runtime-2.8.1\AndroidManifest.xml:116:13-120:29
207                <action android:name="android.intent.action.BOOT_COMPLETED" />
207-->[androidx.work:work-runtime:2.8.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\8975063235ea0ba114f3bb0240765838\transformed\work-runtime-2.8.1\AndroidManifest.xml:117:17-79
207-->[androidx.work:work-runtime:2.8.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\8975063235ea0ba114f3bb0240765838\transformed\work-runtime-2.8.1\AndroidManifest.xml:117:25-76
208                <action android:name="android.intent.action.TIME_SET" />
208-->[androidx.work:work-runtime:2.8.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\8975063235ea0ba114f3bb0240765838\transformed\work-runtime-2.8.1\AndroidManifest.xml:118:17-73
208-->[androidx.work:work-runtime:2.8.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\8975063235ea0ba114f3bb0240765838\transformed\work-runtime-2.8.1\AndroidManifest.xml:118:25-70
209                <action android:name="android.intent.action.TIMEZONE_CHANGED" />
209-->[androidx.work:work-runtime:2.8.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\8975063235ea0ba114f3bb0240765838\transformed\work-runtime-2.8.1\AndroidManifest.xml:119:17-81
209-->[androidx.work:work-runtime:2.8.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\8975063235ea0ba114f3bb0240765838\transformed\work-runtime-2.8.1\AndroidManifest.xml:119:25-78
210            </intent-filter>
211        </receiver>
212        <receiver
212-->[androidx.work:work-runtime:2.8.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\8975063235ea0ba114f3bb0240765838\transformed\work-runtime-2.8.1\AndroidManifest.xml:122:9-131:20
213            android:name="androidx.work.impl.background.systemalarm.ConstraintProxyUpdateReceiver"
213-->[androidx.work:work-runtime:2.8.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\8975063235ea0ba114f3bb0240765838\transformed\work-runtime-2.8.1\AndroidManifest.xml:123:13-99
214            android:directBootAware="false"
214-->[androidx.work:work-runtime:2.8.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\8975063235ea0ba114f3bb0240765838\transformed\work-runtime-2.8.1\AndroidManifest.xml:124:13-44
215            android:enabled="@bool/enable_system_alarm_service_default"
215-->[androidx.work:work-runtime:2.8.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\8975063235ea0ba114f3bb0240765838\transformed\work-runtime-2.8.1\AndroidManifest.xml:125:13-72
216            android:exported="false" >
216-->[androidx.work:work-runtime:2.8.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\8975063235ea0ba114f3bb0240765838\transformed\work-runtime-2.8.1\AndroidManifest.xml:126:13-37
217            <intent-filter>
217-->[androidx.work:work-runtime:2.8.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\8975063235ea0ba114f3bb0240765838\transformed\work-runtime-2.8.1\AndroidManifest.xml:128:13-130:29
218                <action android:name="androidx.work.impl.background.systemalarm.UpdateProxies" />
218-->[androidx.work:work-runtime:2.8.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\8975063235ea0ba114f3bb0240765838\transformed\work-runtime-2.8.1\AndroidManifest.xml:129:17-98
218-->[androidx.work:work-runtime:2.8.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\8975063235ea0ba114f3bb0240765838\transformed\work-runtime-2.8.1\AndroidManifest.xml:129:25-95
219            </intent-filter>
220        </receiver>
221        <receiver
221-->[androidx.work:work-runtime:2.8.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\8975063235ea0ba114f3bb0240765838\transformed\work-runtime-2.8.1\AndroidManifest.xml:132:9-142:20
222            android:name="androidx.work.impl.diagnostics.DiagnosticsReceiver"
222-->[androidx.work:work-runtime:2.8.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\8975063235ea0ba114f3bb0240765838\transformed\work-runtime-2.8.1\AndroidManifest.xml:133:13-78
223            android:directBootAware="false"
223-->[androidx.work:work-runtime:2.8.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\8975063235ea0ba114f3bb0240765838\transformed\work-runtime-2.8.1\AndroidManifest.xml:134:13-44
224            android:enabled="true"
224-->[androidx.work:work-runtime:2.8.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\8975063235ea0ba114f3bb0240765838\transformed\work-runtime-2.8.1\AndroidManifest.xml:135:13-35
225            android:exported="true"
225-->[androidx.work:work-runtime:2.8.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\8975063235ea0ba114f3bb0240765838\transformed\work-runtime-2.8.1\AndroidManifest.xml:136:13-36
226            android:permission="android.permission.DUMP" >
226-->[androidx.work:work-runtime:2.8.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\8975063235ea0ba114f3bb0240765838\transformed\work-runtime-2.8.1\AndroidManifest.xml:137:13-57
227            <intent-filter>
227-->[androidx.work:work-runtime:2.8.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\8975063235ea0ba114f3bb0240765838\transformed\work-runtime-2.8.1\AndroidManifest.xml:139:13-141:29
228                <action android:name="androidx.work.diagnostics.REQUEST_DIAGNOSTICS" />
228-->[androidx.work:work-runtime:2.8.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\8975063235ea0ba114f3bb0240765838\transformed\work-runtime-2.8.1\AndroidManifest.xml:140:17-88
228-->[androidx.work:work-runtime:2.8.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\8975063235ea0ba114f3bb0240765838\transformed\work-runtime-2.8.1\AndroidManifest.xml:140:25-85
229            </intent-filter>
230        </receiver>
231
232        <uses-library
232-->[androidx.window:window:1.2.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\970764a81dd610c772147726baa7dca5\transformed\jetified-window-1.2.0\AndroidManifest.xml:23:9-25:40
233            android:name="androidx.window.extensions"
233-->[androidx.window:window:1.2.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\970764a81dd610c772147726baa7dca5\transformed\jetified-window-1.2.0\AndroidManifest.xml:24:13-54
234            android:required="false" />
234-->[androidx.window:window:1.2.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\970764a81dd610c772147726baa7dca5\transformed\jetified-window-1.2.0\AndroidManifest.xml:25:13-37
235        <uses-library
235-->[androidx.window:window:1.2.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\970764a81dd610c772147726baa7dca5\transformed\jetified-window-1.2.0\AndroidManifest.xml:26:9-28:40
236            android:name="androidx.window.sidecar"
236-->[androidx.window:window:1.2.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\970764a81dd610c772147726baa7dca5\transformed\jetified-window-1.2.0\AndroidManifest.xml:27:13-51
237            android:required="false" />
237-->[androidx.window:window:1.2.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\970764a81dd610c772147726baa7dca5\transformed\jetified-window-1.2.0\AndroidManifest.xml:28:13-37
238
239        <receiver
239-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\eabc1325411a824ae74b9b450245750e\transformed\jetified-profileinstaller-1.3.1\AndroidManifest.xml:34:9-52:20
240            android:name="androidx.profileinstaller.ProfileInstallReceiver"
240-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\eabc1325411a824ae74b9b450245750e\transformed\jetified-profileinstaller-1.3.1\AndroidManifest.xml:35:13-76
241            android:directBootAware="false"
241-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\eabc1325411a824ae74b9b450245750e\transformed\jetified-profileinstaller-1.3.1\AndroidManifest.xml:36:13-44
242            android:enabled="true"
242-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\eabc1325411a824ae74b9b450245750e\transformed\jetified-profileinstaller-1.3.1\AndroidManifest.xml:37:13-35
243            android:exported="true"
243-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\eabc1325411a824ae74b9b450245750e\transformed\jetified-profileinstaller-1.3.1\AndroidManifest.xml:38:13-36
244            android:permission="android.permission.DUMP" >
244-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\eabc1325411a824ae74b9b450245750e\transformed\jetified-profileinstaller-1.3.1\AndroidManifest.xml:39:13-57
245            <intent-filter>
245-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\eabc1325411a824ae74b9b450245750e\transformed\jetified-profileinstaller-1.3.1\AndroidManifest.xml:40:13-42:29
246                <action android:name="androidx.profileinstaller.action.INSTALL_PROFILE" />
246-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\eabc1325411a824ae74b9b450245750e\transformed\jetified-profileinstaller-1.3.1\AndroidManifest.xml:41:17-91
246-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\eabc1325411a824ae74b9b450245750e\transformed\jetified-profileinstaller-1.3.1\AndroidManifest.xml:41:25-88
247            </intent-filter>
248            <intent-filter>
248-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\eabc1325411a824ae74b9b450245750e\transformed\jetified-profileinstaller-1.3.1\AndroidManifest.xml:43:13-45:29
249                <action android:name="androidx.profileinstaller.action.SKIP_FILE" />
249-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\eabc1325411a824ae74b9b450245750e\transformed\jetified-profileinstaller-1.3.1\AndroidManifest.xml:44:17-85
249-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\eabc1325411a824ae74b9b450245750e\transformed\jetified-profileinstaller-1.3.1\AndroidManifest.xml:44:25-82
250            </intent-filter>
251            <intent-filter>
251-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\eabc1325411a824ae74b9b450245750e\transformed\jetified-profileinstaller-1.3.1\AndroidManifest.xml:46:13-48:29
252                <action android:name="androidx.profileinstaller.action.SAVE_PROFILE" />
252-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\eabc1325411a824ae74b9b450245750e\transformed\jetified-profileinstaller-1.3.1\AndroidManifest.xml:47:17-88
252-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\eabc1325411a824ae74b9b450245750e\transformed\jetified-profileinstaller-1.3.1\AndroidManifest.xml:47:25-85
253            </intent-filter>
254            <intent-filter>
254-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\eabc1325411a824ae74b9b450245750e\transformed\jetified-profileinstaller-1.3.1\AndroidManifest.xml:49:13-51:29
255                <action android:name="androidx.profileinstaller.action.BENCHMARK_OPERATION" />
255-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\eabc1325411a824ae74b9b450245750e\transformed\jetified-profileinstaller-1.3.1\AndroidManifest.xml:50:17-95
255-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\eabc1325411a824ae74b9b450245750e\transformed\jetified-profileinstaller-1.3.1\AndroidManifest.xml:50:25-92
256            </intent-filter>
257        </receiver>
258
259        <service
259-->[androidx.room:room-runtime:2.5.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\b0a143534f1d9e3ab757fbcdd209cfbb\transformed\room-runtime-2.5.0\AndroidManifest.xml:24:9-28:63
260            android:name="androidx.room.MultiInstanceInvalidationService"
260-->[androidx.room:room-runtime:2.5.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\b0a143534f1d9e3ab757fbcdd209cfbb\transformed\room-runtime-2.5.0\AndroidManifest.xml:25:13-74
261            android:directBootAware="true"
261-->[androidx.room:room-runtime:2.5.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\b0a143534f1d9e3ab757fbcdd209cfbb\transformed\room-runtime-2.5.0\AndroidManifest.xml:26:13-43
262            android:exported="false" /> <!-- The activities will be merged into the manifest of the hosting app. -->
262-->[androidx.room:room-runtime:2.5.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\b0a143534f1d9e3ab757fbcdd209cfbb\transformed\room-runtime-2.5.0\AndroidManifest.xml:27:13-37
263        <activity
263-->[com.google.android.play:core:1.10.3] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\9b10a4e78f0e7750b24f9632f5e597a2\transformed\jetified-core-1.10.3\AndroidManifest.xml:18:9-24:45
264            android:name="com.google.android.play.core.missingsplits.PlayCoreMissingSplitsActivity"
264-->[com.google.android.play:core:1.10.3] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\9b10a4e78f0e7750b24f9632f5e597a2\transformed\jetified-core-1.10.3\AndroidManifest.xml:19:13-100
265            android:enabled="false"
265-->[com.google.android.play:core:1.10.3] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\9b10a4e78f0e7750b24f9632f5e597a2\transformed\jetified-core-1.10.3\AndroidManifest.xml:20:13-36
266            android:exported="false"
266-->[com.google.android.play:core:1.10.3] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\9b10a4e78f0e7750b24f9632f5e597a2\transformed\jetified-core-1.10.3\AndroidManifest.xml:21:13-37
267            android:launchMode="singleInstance"
267-->[com.google.android.play:core:1.10.3] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\9b10a4e78f0e7750b24f9632f5e597a2\transformed\jetified-core-1.10.3\AndroidManifest.xml:22:13-48
268            android:process=":playcore_missing_splits_activity"
268-->[com.google.android.play:core:1.10.3] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\9b10a4e78f0e7750b24f9632f5e597a2\transformed\jetified-core-1.10.3\AndroidManifest.xml:23:13-64
269            android:stateNotNeeded="true" />
269-->[com.google.android.play:core:1.10.3] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\9b10a4e78f0e7750b24f9632f5e597a2\transformed\jetified-core-1.10.3\AndroidManifest.xml:24:13-42
270        <activity
270-->[com.google.android.play:core:1.10.3] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\9b10a4e78f0e7750b24f9632f5e597a2\transformed\jetified-core-1.10.3\AndroidManifest.xml:25:9-29:65
271            android:name="com.google.android.play.core.common.PlayCoreDialogWrapperActivity"
271-->[com.google.android.play:core:1.10.3] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\9b10a4e78f0e7750b24f9632f5e597a2\transformed\jetified-core-1.10.3\AndroidManifest.xml:26:13-93
272            android:exported="false"
272-->[com.google.android.play:core:1.10.3] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\9b10a4e78f0e7750b24f9632f5e597a2\transformed\jetified-core-1.10.3\AndroidManifest.xml:27:13-37
273            android:stateNotNeeded="true"
273-->[com.google.android.play:core:1.10.3] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\9b10a4e78f0e7750b24f9632f5e597a2\transformed\jetified-core-1.10.3\AndroidManifest.xml:28:13-42
274            android:theme="@style/Theme.PlayCore.Transparent" /> <!-- The services will be merged into the manifest of the hosting app. -->
274-->[com.google.android.play:core:1.10.3] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\9b10a4e78f0e7750b24f9632f5e597a2\transformed\jetified-core-1.10.3\AndroidManifest.xml:29:13-62
275        <service
275-->[com.google.android.play:core:1.10.3] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\9b10a4e78f0e7750b24f9632f5e597a2\transformed\jetified-core-1.10.3\AndroidManifest.xml:32:9-39:19
276            android:name="com.google.android.play.core.assetpacks.AssetPackExtractionService"
276-->[com.google.android.play:core:1.10.3] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\9b10a4e78f0e7750b24f9632f5e597a2\transformed\jetified-core-1.10.3\AndroidManifest.xml:33:13-94
277            android:enabled="false"
277-->[com.google.android.play:core:1.10.3] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\9b10a4e78f0e7750b24f9632f5e597a2\transformed\jetified-core-1.10.3\AndroidManifest.xml:34:13-36
278            android:exported="true" >
278-->[com.google.android.play:core:1.10.3] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\9b10a4e78f0e7750b24f9632f5e597a2\transformed\jetified-core-1.10.3\AndroidManifest.xml:35:13-36
279            <meta-data
279-->[com.google.android.play:core:1.10.3] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\9b10a4e78f0e7750b24f9632f5e597a2\transformed\jetified-core-1.10.3\AndroidManifest.xml:36:13-38:41
280                android:name="com.google.android.play.core.assetpacks.versionCode"
280-->[com.google.android.play:core:1.10.3] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\9b10a4e78f0e7750b24f9632f5e597a2\transformed\jetified-core-1.10.3\AndroidManifest.xml:37:17-83
281                android:value="11003" />
281-->[com.google.android.play:core:1.10.3] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\9b10a4e78f0e7750b24f9632f5e597a2\transformed\jetified-core-1.10.3\AndroidManifest.xml:38:17-38
282        </service>
283        <service
283-->[com.google.android.play:core:1.10.3] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\9b10a4e78f0e7750b24f9632f5e597a2\transformed\jetified-core-1.10.3\AndroidManifest.xml:40:9-43:40
284            android:name="com.google.android.play.core.assetpacks.ExtractionForegroundService"
284-->[com.google.android.play:core:1.10.3] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\9b10a4e78f0e7750b24f9632f5e597a2\transformed\jetified-core-1.10.3\AndroidManifest.xml:41:13-95
285            android:enabled="false"
285-->[com.google.android.play:core:1.10.3] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\9b10a4e78f0e7750b24f9632f5e597a2\transformed\jetified-core-1.10.3\AndroidManifest.xml:42:13-36
286            android:exported="false" />
286-->[com.google.android.play:core:1.10.3] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\9b10a4e78f0e7750b24f9632f5e597a2\transformed\jetified-core-1.10.3\AndroidManifest.xml:43:13-37
287    </application>
288
289</manifest>
