<lint-module
    format="1"
    dir="C:\Users\<USER>\AppData\Local\Pub\Cache\hosted\pub.dev\flutter_local_notifications-19.1.0\android"
    name=":flutter_local_notifications"
    type="LIBRARY"
    maven="com.dexterous.flutterlocalnotifications:flutter_local_notifications:1.0-SNAPSHOT"
    agpVersion="8.7.0"
    buildFolder="D:\PROGRAMMING\AC-FIT\acfit_app\build\flutter_local_notifications"
    bootClassPath="C:\Users\<USER>\AppData\Local\Android\sdk\platforms\android-35\android.jar;C:\Users\<USER>\AppData\Local\Android\sdk\build-tools\34.0.0\core-lambda-stubs.jar"
    javaSourceLevel="11"
    compileTarget="android-35"
    neverShrinking="true">
  <lintOptions
      disable="InvalidPackage"
      abortOnError="true"
      absolutePaths="true"
      checkReleaseBuilds="true"
      explainIssues="true">
    <severities>
      <severity
        id="InvalidPackage"
        severity="IGNORE" />
    </severities>
  </lintOptions>
  <variant name="release"/>
</lint-module>
