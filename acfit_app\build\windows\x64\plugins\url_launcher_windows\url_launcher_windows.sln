﻿
Microsoft Visual Studio Solution File, Format Version 12.00
# Visual Studio Version 17
Project("{8BC9CEB8-8B4A-11D0-8D11-00A0C91BC942}") = "ALL_BUILD", "ALL_BUILD.vcxproj", "{CBA10E65-51D8-36C6-AEB6-DBEDF1D2950C}"
	ProjectSection(ProjectDependencies) = postProject
		{FFCED161-E4F3-3C8F-9A21-C1EBE5EFE78A} = {FFCED161-E4F3-3C8F-9A21-C1EBE5EFE78A}
		{B77A7839-2464-3FC3-9B6A-F3DD6E854AD2} = {B77A7839-2464-3FC3-9B6A-F3DD6E854AD2}
	EndProjectSection
EndProject
Project("{8BC9CEB8-8B4A-11D0-8D11-00A0C91BC942}") = "INSTALL", "INSTALL.vcxproj", "{4712F87E-E495-3E6B-AA29-F8AC22FF917F}"
	ProjectSection(ProjectDependencies) = postProject
		{CBA10E65-51D8-36C6-AEB6-DBEDF1D2950C} = {CBA10E65-51D8-36C6-AEB6-DBEDF1D2950C}
		{FFCED161-E4F3-3C8F-9A21-C1EBE5EFE78A} = {FFCED161-E4F3-3C8F-9A21-C1EBE5EFE78A}
	EndProjectSection
EndProject
Project("{8BC9CEB8-8B4A-11D0-8D11-00A0C91BC942}") = "ZERO_CHECK", "..\..\\ZERO_CHECK.vcxproj", "{FFCED161-E4F3-3C8F-9A21-C1EBE5EFE78A}"
	ProjectSection(ProjectDependencies) = postProject
	EndProjectSection
EndProject
Project("{8BC9CEB8-8B4A-11D0-8D11-00A0C91BC942}") = "flutter_assemble", "..\..\flutter\flutter_assemble.vcxproj", "{66CEF379-F235-3E6F-9C51-782657F6D12E}"
	ProjectSection(ProjectDependencies) = postProject
		{FFCED161-E4F3-3C8F-9A21-C1EBE5EFE78A} = {FFCED161-E4F3-3C8F-9A21-C1EBE5EFE78A}
	EndProjectSection
EndProject
Project("{8BC9CEB8-8B4A-11D0-8D11-00A0C91BC942}") = "flutter_wrapper_plugin", "..\..\flutter\flutter_wrapper_plugin.vcxproj", "{C7B264A8-51E0-3940-8BF3-C1828977501A}"
	ProjectSection(ProjectDependencies) = postProject
		{FFCED161-E4F3-3C8F-9A21-C1EBE5EFE78A} = {FFCED161-E4F3-3C8F-9A21-C1EBE5EFE78A}
		{66CEF379-F235-3E6F-9C51-782657F6D12E} = {66CEF379-F235-3E6F-9C51-782657F6D12E}
	EndProjectSection
EndProject
Project("{8BC9CEB8-8B4A-11D0-8D11-00A0C91BC942}") = "url_launcher_windows_plugin", "url_launcher_windows_plugin.vcxproj", "{B77A7839-2464-3FC3-9B6A-F3DD6E854AD2}"
	ProjectSection(ProjectDependencies) = postProject
		{FFCED161-E4F3-3C8F-9A21-C1EBE5EFE78A} = {FFCED161-E4F3-3C8F-9A21-C1EBE5EFE78A}
		{66CEF379-F235-3E6F-9C51-782657F6D12E} = {66CEF379-F235-3E6F-9C51-782657F6D12E}
		{C7B264A8-51E0-3940-8BF3-C1828977501A} = {C7B264A8-51E0-3940-8BF3-C1828977501A}
	EndProjectSection
EndProject
Global
	GlobalSection(SolutionConfigurationPlatforms) = preSolution
		Debug|x64 = Debug|x64
		Profile|x64 = Profile|x64
		Release|x64 = Release|x64
	EndGlobalSection
	GlobalSection(ProjectConfigurationPlatforms) = postSolution
		{CBA10E65-51D8-36C6-AEB6-DBEDF1D2950C}.Debug|x64.ActiveCfg = Debug|x64
		{CBA10E65-51D8-36C6-AEB6-DBEDF1D2950C}.Debug|x64.Build.0 = Debug|x64
		{CBA10E65-51D8-36C6-AEB6-DBEDF1D2950C}.Profile|x64.ActiveCfg = Profile|x64
		{CBA10E65-51D8-36C6-AEB6-DBEDF1D2950C}.Profile|x64.Build.0 = Profile|x64
		{CBA10E65-51D8-36C6-AEB6-DBEDF1D2950C}.Release|x64.ActiveCfg = Release|x64
		{CBA10E65-51D8-36C6-AEB6-DBEDF1D2950C}.Release|x64.Build.0 = Release|x64
		{4712F87E-E495-3E6B-AA29-F8AC22FF917F}.Debug|x64.ActiveCfg = Debug|x64
		{4712F87E-E495-3E6B-AA29-F8AC22FF917F}.Profile|x64.ActiveCfg = Profile|x64
		{4712F87E-E495-3E6B-AA29-F8AC22FF917F}.Release|x64.ActiveCfg = Release|x64
		{FFCED161-E4F3-3C8F-9A21-C1EBE5EFE78A}.Debug|x64.ActiveCfg = Debug|x64
		{FFCED161-E4F3-3C8F-9A21-C1EBE5EFE78A}.Debug|x64.Build.0 = Debug|x64
		{FFCED161-E4F3-3C8F-9A21-C1EBE5EFE78A}.Profile|x64.ActiveCfg = Profile|x64
		{FFCED161-E4F3-3C8F-9A21-C1EBE5EFE78A}.Profile|x64.Build.0 = Profile|x64
		{FFCED161-E4F3-3C8F-9A21-C1EBE5EFE78A}.Release|x64.ActiveCfg = Release|x64
		{FFCED161-E4F3-3C8F-9A21-C1EBE5EFE78A}.Release|x64.Build.0 = Release|x64
		{66CEF379-F235-3E6F-9C51-782657F6D12E}.Debug|x64.ActiveCfg = Debug|x64
		{66CEF379-F235-3E6F-9C51-782657F6D12E}.Debug|x64.Build.0 = Debug|x64
		{66CEF379-F235-3E6F-9C51-782657F6D12E}.Profile|x64.ActiveCfg = Profile|x64
		{66CEF379-F235-3E6F-9C51-782657F6D12E}.Profile|x64.Build.0 = Profile|x64
		{66CEF379-F235-3E6F-9C51-782657F6D12E}.Release|x64.ActiveCfg = Release|x64
		{66CEF379-F235-3E6F-9C51-782657F6D12E}.Release|x64.Build.0 = Release|x64
		{C7B264A8-51E0-3940-8BF3-C1828977501A}.Debug|x64.ActiveCfg = Debug|x64
		{C7B264A8-51E0-3940-8BF3-C1828977501A}.Debug|x64.Build.0 = Debug|x64
		{C7B264A8-51E0-3940-8BF3-C1828977501A}.Profile|x64.ActiveCfg = Profile|x64
		{C7B264A8-51E0-3940-8BF3-C1828977501A}.Profile|x64.Build.0 = Profile|x64
		{C7B264A8-51E0-3940-8BF3-C1828977501A}.Release|x64.ActiveCfg = Release|x64
		{C7B264A8-51E0-3940-8BF3-C1828977501A}.Release|x64.Build.0 = Release|x64
		{B77A7839-2464-3FC3-9B6A-F3DD6E854AD2}.Debug|x64.ActiveCfg = Debug|x64
		{B77A7839-2464-3FC3-9B6A-F3DD6E854AD2}.Debug|x64.Build.0 = Debug|x64
		{B77A7839-2464-3FC3-9B6A-F3DD6E854AD2}.Profile|x64.ActiveCfg = Profile|x64
		{B77A7839-2464-3FC3-9B6A-F3DD6E854AD2}.Profile|x64.Build.0 = Profile|x64
		{B77A7839-2464-3FC3-9B6A-F3DD6E854AD2}.Release|x64.ActiveCfg = Release|x64
		{B77A7839-2464-3FC3-9B6A-F3DD6E854AD2}.Release|x64.Build.0 = Release|x64
	EndGlobalSection
	GlobalSection(ExtensibilityGlobals) = postSolution
		SolutionGuid = {BD607BA1-6BC9-3E4E-B10F-7BC3DCC46D88}
	EndGlobalSection
	GlobalSection(ExtensibilityAddIns) = postSolution
	EndGlobalSection
EndGlobal
