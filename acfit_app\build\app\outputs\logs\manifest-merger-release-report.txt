-- Merging decision tree log ---
application
INJECTED from D:\PROGRAMMING\AC-FIT\acfit_app\android\app\src\main\AndroidManifest.xml:4:5-37:19
INJECTED from D:\PROGRAMMING\AC-FIT\acfit_app\android\app\src\main\AndroidManifest.xml:4:5-37:19
MERGED from [:image_picker_android] D:\PROGRAMMING\AC-FIT\acfit_app\build\image_picker_android\intermediates\merged_manifest\release\processReleaseManifest\AndroidManifest.xml:8:5-32:19
MERGED from [:image_picker_android] D:\PROGRAMMING\AC-FIT\acfit_app\build\image_picker_android\intermediates\merged_manifest\release\processReleaseManifest\AndroidManifest.xml:8:5-32:19
MERGED from [:url_launcher_android] D:\PROGRAMMING\AC-FIT\acfit_app\build\url_launcher_android\intermediates\merged_manifest\release\processReleaseManifest\AndroidManifest.xml:7:5-12:19
MERGED from [:url_launcher_android] D:\PROGRAMMING\AC-FIT\acfit_app\build\url_launcher_android\intermediates\merged_manifest\release\processReleaseManifest\AndroidManifest.xml:7:5-12:19
MERGED from [androidx.work:work-runtime:2.8.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\8975063235ea0ba114f3bb0240765838\transformed\work-runtime-2.8.1\AndroidManifest.xml:28:5-143:19
MERGED from [androidx.work:work-runtime:2.8.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\8975063235ea0ba114f3bb0240765838\transformed\work-runtime-2.8.1\AndroidManifest.xml:28:5-143:19
MERGED from [androidx.window:window:1.2.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\970764a81dd610c772147726baa7dca5\transformed\jetified-window-1.2.0\AndroidManifest.xml:22:5-29:19
MERGED from [androidx.window:window:1.2.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\970764a81dd610c772147726baa7dca5\transformed\jetified-window-1.2.0\AndroidManifest.xml:22:5-29:19
MERGED from [androidx.lifecycle:lifecycle-process:2.7.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\e920d985392df03e5b02e06877a52d21\transformed\jetified-lifecycle-process-2.7.0\AndroidManifest.xml:23:5-33:19
MERGED from [androidx.lifecycle:lifecycle-process:2.7.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\e920d985392df03e5b02e06877a52d21\transformed\jetified-lifecycle-process-2.7.0\AndroidManifest.xml:23:5-33:19
MERGED from [androidx.core:core:1.13.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\deb838f5c4813d0c441214e1ddf497cc\transformed\core-1.13.1\AndroidManifest.xml:28:5-89
MERGED from [androidx.core:core:1.13.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\deb838f5c4813d0c441214e1ddf497cc\transformed\core-1.13.1\AndroidManifest.xml:28:5-89
MERGED from [androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\eabc1325411a824ae74b9b450245750e\transformed\jetified-profileinstaller-1.3.1\AndroidManifest.xml:23:5-53:19
MERGED from [androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\eabc1325411a824ae74b9b450245750e\transformed\jetified-profileinstaller-1.3.1\AndroidManifest.xml:23:5-53:19
MERGED from [androidx.startup:startup-runtime:1.1.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\77d6589d54f70503af1ad7bd5c95eb07\transformed\jetified-startup-runtime-1.1.1\AndroidManifest.xml:25:5-31:19
MERGED from [androidx.startup:startup-runtime:1.1.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\77d6589d54f70503af1ad7bd5c95eb07\transformed\jetified-startup-runtime-1.1.1\AndroidManifest.xml:25:5-31:19
MERGED from [androidx.versionedparcelable:versionedparcelable:1.1.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\ae03ddb689964a197cc9572a48c09923\transformed\versionedparcelable-1.1.1\AndroidManifest.xml:24:5-25:19
MERGED from [androidx.versionedparcelable:versionedparcelable:1.1.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\ae03ddb689964a197cc9572a48c09923\transformed\versionedparcelable-1.1.1\AndroidManifest.xml:24:5-25:19
MERGED from [androidx.room:room-runtime:2.5.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\b0a143534f1d9e3ab757fbcdd209cfbb\transformed\room-runtime-2.5.0\AndroidManifest.xml:23:5-29:19
MERGED from [androidx.room:room-runtime:2.5.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\b0a143534f1d9e3ab757fbcdd209cfbb\transformed\room-runtime-2.5.0\AndroidManifest.xml:23:5-29:19
MERGED from [com.google.android.play:core:1.10.3] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\9b10a4e78f0e7750b24f9632f5e597a2\transformed\jetified-core-1.10.3\AndroidManifest.xml:15:5-44:19
MERGED from [com.google.android.play:core:1.10.3] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\9b10a4e78f0e7750b24f9632f5e597a2\transformed\jetified-core-1.10.3\AndroidManifest.xml:15:5-44:19
	android:extractNativeLibs
		INJECTED from D:\PROGRAMMING\AC-FIT\acfit_app\android\app\src\main\AndroidManifest.xml
	android:appComponentFactory
		ADDED from [androidx.core:core:1.13.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\deb838f5c4813d0c441214e1ddf497cc\transformed\core-1.13.1\AndroidManifest.xml:28:18-86
	android:label
		ADDED from D:\PROGRAMMING\AC-FIT\acfit_app\android\app\src\main\AndroidManifest.xml:5:9-35
	android:icon
		ADDED from D:\PROGRAMMING\AC-FIT\acfit_app\android\app\src\main\AndroidManifest.xml:7:9-43
	android:enableOnBackInvokedCallback
		ADDED from D:\PROGRAMMING\AC-FIT\acfit_app\android\app\src\main\AndroidManifest.xml:9:9-51
	android:usesCleartextTraffic
		ADDED from D:\PROGRAMMING\AC-FIT\acfit_app\android\app\src\main\AndroidManifest.xml:8:9-44
	android:name
		INJECTED from D:\PROGRAMMING\AC-FIT\acfit_app\android\app\src\main\AndroidManifest.xml
		ADDED from D:\PROGRAMMING\AC-FIT\acfit_app\android\app\src\main\AndroidManifest.xml:6:9-42
manifest
ADDED from D:\PROGRAMMING\AC-FIT\acfit_app\android\app\src\main\AndroidManifest.xml:1:1-61:12
INJECTED from D:\PROGRAMMING\AC-FIT\acfit_app\android\app\src\main\AndroidManifest.xml:1:1-61:12
INJECTED from D:\PROGRAMMING\AC-FIT\acfit_app\android\app\src\main\AndroidManifest.xml:1:1-61:12
INJECTED from D:\PROGRAMMING\AC-FIT\acfit_app\android\app\src\main\AndroidManifest.xml:1:1-61:12
MERGED from [:flutter_timezone] D:\PROGRAMMING\AC-FIT\acfit_app\build\flutter_timezone\intermediates\merged_manifest\release\processReleaseManifest\AndroidManifest.xml:2:1-7:12
MERGED from [:shared_preferences_android] D:\PROGRAMMING\AC-FIT\acfit_app\build\shared_preferences_android\intermediates\merged_manifest\release\processReleaseManifest\AndroidManifest.xml:2:1-7:12
MERGED from [:wakelock_plus] D:\PROGRAMMING\AC-FIT\acfit_app\build\wakelock_plus\intermediates\merged_manifest\release\processReleaseManifest\AndroidManifest.xml:2:1-7:12
MERGED from [:package_info_plus] D:\PROGRAMMING\AC-FIT\acfit_app\build\package_info_plus\intermediates\merged_manifest\release\processReleaseManifest\AndroidManifest.xml:2:1-7:12
MERGED from [:audioplayers_android] D:\PROGRAMMING\AC-FIT\acfit_app\build\audioplayers_android\intermediates\merged_manifest\release\processReleaseManifest\AndroidManifest.xml:2:1-7:12
MERGED from [:workmanager] D:\PROGRAMMING\AC-FIT\acfit_app\build\workmanager\intermediates\merged_manifest\release\processReleaseManifest\AndroidManifest.xml:2:1-7:12
MERGED from [:flutter_local_notifications] D:\PROGRAMMING\AC-FIT\acfit_app\build\flutter_local_notifications\intermediates\merged_manifest\release\processReleaseManifest\AndroidManifest.xml:2:1-10:12
MERGED from [:image_picker_android] D:\PROGRAMMING\AC-FIT\acfit_app\build\image_picker_android\intermediates\merged_manifest\release\processReleaseManifest\AndroidManifest.xml:2:1-34:12
MERGED from [:url_launcher_android] D:\PROGRAMMING\AC-FIT\acfit_app\build\url_launcher_android\intermediates\merged_manifest\release\processReleaseManifest\AndroidManifest.xml:2:1-14:12
MERGED from [:connectivity_plus] D:\PROGRAMMING\AC-FIT\acfit_app\build\connectivity_plus\intermediates\merged_manifest\release\processReleaseManifest\AndroidManifest.xml:2:1-9:12
MERGED from [:flutter_plugin_android_lifecycle] D:\PROGRAMMING\AC-FIT\acfit_app\build\flutter_plugin_android_lifecycle\intermediates\merged_manifest\release\processReleaseManifest\AndroidManifest.xml:2:1-7:12
MERGED from [:flutter_secure_storage] D:\PROGRAMMING\AC-FIT\acfit_app\build\flutter_secure_storage\intermediates\merged_manifest\release\processReleaseManifest\AndroidManifest.xml:2:1-10:12
MERGED from [:path_provider_android] D:\PROGRAMMING\AC-FIT\acfit_app\build\path_provider_android\intermediates\merged_manifest\release\processReleaseManifest\AndroidManifest.xml:2:1-7:12
MERGED from [:sqflite_android] D:\PROGRAMMING\AC-FIT\acfit_app\build\sqflite_android\intermediates\merged_manifest\release\processReleaseManifest\AndroidManifest.xml:2:1-7:12
MERGED from [:video_player_android] D:\PROGRAMMING\AC-FIT\acfit_app\build\video_player_android\intermediates\merged_manifest\release\processReleaseManifest\AndroidManifest.xml:2:1-7:12
MERGED from [androidx.media:media:1.1.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\9306062905f4d3cda1670c0e5f3df17e\transformed\media-1.1.0\AndroidManifest.xml:17:1-24:12
MERGED from [androidx.preference:preference:1.2.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\0aea3dee6044b352a54f8fb9c4a12c07\transformed\preference-1.2.1\AndroidManifest.xml:17:1-24:12
MERGED from [androidx.appcompat:appcompat:1.1.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\32d7d149835c8c00f0697dfcab9adb84\transformed\appcompat-1.1.0\AndroidManifest.xml:17:1-24:12
MERGED from [androidx.fragment:fragment-ktx:1.7.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\dd8f2b9cf49e7284b6695565297622e3\transformed\jetified-fragment-ktx-1.7.1\AndroidManifest.xml:2:1-7:12
MERGED from [androidx.fragment:fragment:1.7.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\812b0debb255957f3fcc9a2669f27c22\transformed\fragment-1.7.1\AndroidManifest.xml:2:1-7:12
MERGED from [androidx.activity:activity-ktx:1.9.3] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\4d0eff89ceab5cf34de5203e536a9f16\transformed\jetified-activity-ktx-1.9.3\AndroidManifest.xml:2:1-7:12
MERGED from [androidx.activity:activity:1.9.3] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\e57d2fe08522bc82b0ac6f830a2f178d\transformed\jetified-activity-1.9.3\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.browser:browser:1.8.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\5683f3a75f4aae74864d56d25d09ab49\transformed\browser-1.8.0\AndroidManifest.xml:2:1-7:12
MERGED from [androidx.media3:media3-extractor:1.4.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\0507ffafbde2f9a8ad624d19df0920ce\transformed\jetified-media3-extractor-1.4.1\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.media3:media3-container:1.4.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\d0e1ce7a6f4fbc4be2e75726209a7737\transformed\jetified-media3-container-1.4.1\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.media3:media3-datasource:1.4.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\1662d0a82ced6df7352db1ad102f719f\transformed\jetified-media3-datasource-1.4.1\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.media3:media3-decoder:1.4.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\7ba5ac15d51f0344948a46db4a42d918\transformed\jetified-media3-decoder-1.4.1\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.media3:media3-database:1.4.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\e708d741ac9b6c15a552df6302769687\transformed\jetified-media3-database-1.4.1\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.media3:media3-common:1.4.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\0bdc39bce25c90ffaf84752415c05baa\transformed\jetified-media3-common-1.4.1\AndroidManifest.xml:17:1-24:12
MERGED from [androidx.media3:media3-exoplayer-hls:1.4.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\7d358ae40d5f7529b531bcb8822e904a\transformed\jetified-media3-exoplayer-hls-1.4.1\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.media3:media3-exoplayer-dash:1.4.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\5416cf3532cb6ded6f8189dbf43ae176\transformed\jetified-media3-exoplayer-dash-1.4.1\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.media3:media3-exoplayer-rtsp:1.4.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\9c52213adb527094bb6979d4146926a6\transformed\jetified-media3-exoplayer-rtsp-1.4.1\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.media3:media3-exoplayer-smoothstreaming:1.4.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\442c4d5a4d19f6310c30b463bc3f448d\transformed\jetified-media3-exoplayer-smoothstreaming-1.4.1\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.media3:media3-exoplayer:1.4.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\f3900c31e436e087be46264d65f1dea9\transformed\jetified-media3-exoplayer-1.4.1\AndroidManifest.xml:17:1-24:12
MERGED from [androidx.work:work-runtime:2.8.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\8975063235ea0ba114f3bb0240765838\transformed\work-runtime-2.8.1\AndroidManifest.xml:17:1-145:12
MERGED from [androidx.recyclerview:recyclerview:1.0.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\b2df645d79e711076ea45b6a8385e99c\transformed\recyclerview-1.0.0\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.legacy:legacy-support-core-ui:1.0.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\6fb5bbbc1627b43bb066b846f50b0b9c\transformed\legacy-support-core-ui-1.0.0\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.slidingpanelayout:slidingpanelayout:1.2.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\f3ef3caec9d424994a2628e26767d1d4\transformed\slidingpanelayout-1.2.0\AndroidManifest.xml:17:1-24:12
MERGED from [androidx.window:window:1.2.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\970764a81dd610c772147726baa7dca5\transformed\jetified-window-1.2.0\AndroidManifest.xml:17:1-31:12
MERGED from [androidx.window:window-java:1.2.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\2a5284c6eeabc2d8b6190c9576956065\transformed\jetified-window-java-1.2.0\AndroidManifest.xml:17:1-21:12
MERGED from [androidx.legacy:legacy-support-core-utils:1.0.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\4c4a6ad3f7b10ac103c0346368799501\transformed\legacy-support-core-utils-1.0.0\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.loader:loader:1.0.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\cf593104563b66144c3b6dc7fc4802c1\transformed\loader-1.0.0\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.lifecycle:lifecycle-livedata-core-ktx:2.7.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\645c56368631721d8b7755f7e8a726a8\transformed\jetified-lifecycle-livedata-core-ktx-2.7.0\AndroidManifest.xml:2:1-7:12
MERGED from [androidx.lifecycle:lifecycle-viewmodel-ktx:2.7.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\d8c201c583c6fe469398993e61fede1c\transformed\jetified-lifecycle-viewmodel-ktx-2.7.0\AndroidManifest.xml:2:1-7:12
MERGED from [androidx.lifecycle:lifecycle-runtime-ktx:2.7.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\3ff6adcf87eb4959e12e2c03b49ab87a\transformed\jetified-lifecycle-runtime-ktx-2.7.0\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.lifecycle:lifecycle-livedata-core:2.7.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\07de910142823f029a6feac220e8ca4a\transformed\lifecycle-livedata-core-2.7.0\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.lifecycle:lifecycle-viewmodel:2.7.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\594b8e088512a24631e5a37668efcdba\transformed\lifecycle-viewmodel-2.7.0\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.lifecycle:lifecycle-service:2.7.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\e18fb94549ccb4f6a794ad42ccd1ec5d\transformed\jetified-lifecycle-service-2.7.0\AndroidManifest.xml:2:1-7:12
MERGED from [androidx.lifecycle:lifecycle-livedata:2.7.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\7c8820905eb370fbd3bb42d26ba8ae5a\transformed\lifecycle-livedata-2.7.0\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.lifecycle:lifecycle-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\3406eb4cffe3e53bb6d20996e0b4ec87\transformed\lifecycle-runtime-2.7.0\AndroidManifest.xml:2:1-7:12
MERGED from [androidx.lifecycle:lifecycle-process:2.7.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\e920d985392df03e5b02e06877a52d21\transformed\jetified-lifecycle-process-2.7.0\AndroidManifest.xml:17:1-35:12
MERGED from [androidx.savedstate:savedstate-ktx:1.2.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\7f3b092f04c0253e979c83421f120f88\transformed\jetified-savedstate-ktx-1.2.1\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.savedstate:savedstate:1.2.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\ae5f57bd372fc0b34c3bf33092f8edd1\transformed\jetified-savedstate-1.2.1\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.lifecycle:lifecycle-viewmodel-savedstate:2.7.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\834fbfef458f2f76792d20bc77011687\transformed\jetified-lifecycle-viewmodel-savedstate-2.7.0\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.core:core-ktx:1.13.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\538273fdc918a79c3b54859282434b2c\transformed\jetified-core-ktx-1.13.1\AndroidManifest.xml:2:1-7:12
MERGED from [androidx.viewpager:viewpager:1.0.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\e71467fe7caeb25f99b2fabbeba6edee\transformed\viewpager-1.0.0\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.appcompat:appcompat-resources:1.1.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\1ff8cdab1140e9ed76ee2cc1c84e5b27\transformed\jetified-appcompat-resources-1.1.0\AndroidManifest.xml:17:1-24:12
MERGED from [androidx.drawerlayout:drawerlayout:1.0.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\9108df622b0b8e8dbba78b566c3bdc67\transformed\drawerlayout-1.0.0\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.coordinatorlayout:coordinatorlayout:1.0.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\b6435e177046c519b4e9a09d53555b3f\transformed\coordinatorlayout-1.0.0\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.customview:customview:1.1.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\348e9995eb75a687bf31011ee5dfb249\transformed\customview-1.1.0\AndroidManifest.xml:17:1-24:12
MERGED from [androidx.transition:transition:1.4.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\3e9c3e49153aa47f3a49791ab647f926\transformed\transition-1.4.1\AndroidManifest.xml:17:1-24:12
MERGED from [androidx.vectordrawable:vectordrawable-animated:1.1.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\0fa1677c034ef049b8c12b4202dcc8c4\transformed\vectordrawable-animated-1.1.0\AndroidManifest.xml:17:1-24:12
MERGED from [androidx.vectordrawable:vectordrawable:1.1.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\2111a692760d2213c419f21507346c83\transformed\vectordrawable-1.1.0\AndroidManifest.xml:17:1-24:12
MERGED from [androidx.swiperefreshlayout:swiperefreshlayout:1.0.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\bbe59546d7834ae0953d7d1775d43fac\transformed\swiperefreshlayout-1.0.0\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.asynclayoutinflater:asynclayoutinflater:1.0.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\7295a6e9f96a0daeed321ae2faac1f7b\transformed\asynclayoutinflater-1.0.0\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.core:core:1.13.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\deb838f5c4813d0c441214e1ddf497cc\transformed\core-1.13.1\AndroidManifest.xml:17:1-30:12
MERGED from [androidx.security:security-crypto:1.1.0-alpha06] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\aefd614a712efdaf0e306e9a9f70c30d\transformed\jetified-security-crypto-1.1.0-alpha06\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.exifinterface:exifinterface:1.3.7] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\747df56e3edd1e12435e5eae9046f54c\transformed\exifinterface-1.3.7\AndroidManifest.xml:17:1-24:12
MERGED from [androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\eabc1325411a824ae74b9b450245750e\transformed\jetified-profileinstaller-1.3.1\AndroidManifest.xml:17:1-55:12
MERGED from [androidx.startup:startup-runtime:1.1.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\77d6589d54f70503af1ad7bd5c95eb07\transformed\jetified-startup-runtime-1.1.1\AndroidManifest.xml:17:1-33:12
MERGED from [androidx.tracing:tracing:1.2.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\67e6cf8dd6496a1e1ab42bfbc69e3371\transformed\jetified-tracing-1.2.0\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.interpolator:interpolator:1.0.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\7620a15c947fbbae2a35989fac4ced29\transformed\interpolator-1.0.0\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.versionedparcelable:versionedparcelable:1.1.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\ae03ddb689964a197cc9572a48c09923\transformed\versionedparcelable-1.1.1\AndroidManifest.xml:17:1-27:12
MERGED from [androidx.datastore:datastore-core-android:1.1.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\7c345815c222fade1dd4a11ff4988234\transformed\jetified-datastore-core-release\AndroidManifest.xml:2:1-7:12
MERGED from [androidx.datastore:datastore-preferences-android:1.1.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\416d2416b9816fecc86610fc0bc7c7ac\transformed\jetified-datastore-preferences-release\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.datastore:datastore-android:1.1.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\e97941c4f2767d0e57cf200fdcb26d11\transformed\jetified-datastore-release\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.room:room-runtime:2.5.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\b0a143534f1d9e3ab757fbcdd209cfbb\transformed\room-runtime-2.5.0\AndroidManifest.xml:17:1-31:12
MERGED from [androidx.sqlite:sqlite-framework:2.3.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\584ffc75cc83b4f6d7ebd88158cc055e\transformed\sqlite-framework-2.3.0\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.arch.core:core-runtime:2.2.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\24b2d70c983456e4678be6d7afa07780\transformed\core-runtime-2.2.0\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.cursoradapter:cursoradapter:1.0.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\cdf4cc3b708776cdc2805e90540cbeb3\transformed\cursoradapter-1.0.0\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.sqlite:sqlite:2.3.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\e4002a4a3b92ef231a557362b3b46ea2\transformed\sqlite-2.3.0\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.window.extensions.core:core:1.0.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\55a192cd1e8c493370fa6465d68c1145\transformed\jetified-core-1.0.0\AndroidManifest.xml:2:1-7:12
MERGED from [androidx.documentfile:documentfile:1.0.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\38895ff92e9ea1cab9c97b52c490e983\transformed\documentfile-1.0.0\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.localbroadcastmanager:localbroadcastmanager:1.0.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\14a7b710af8666cefae7796bd89d044c\transformed\localbroadcastmanager-1.0.0\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.print:print:1.0.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\adc76dc2b16c544bb53f946048eea1be\transformed\print-1.0.0\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.annotation:annotation-experimental:1.4.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\99ae6e67fc3e457914a47c035e30c12a\transformed\jetified-annotation-experimental-1.4.0\AndroidManifest.xml:2:1-7:12
MERGED from [com.google.android.play:core:1.10.3] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\9b10a4e78f0e7750b24f9632f5e597a2\transformed\jetified-core-1.10.3\AndroidManifest.xml:2:1-46:12
MERGED from [com.getkeepsafe.relinker:relinker:1.4.5] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\6a9ec07ac357bc15a8d85f12e6d4189c\transformed\jetified-relinker-1.4.5\AndroidManifest.xml:2:1-7:12
	package
		INJECTED from D:\PROGRAMMING\AC-FIT\acfit_app\android\app\src\main\AndroidManifest.xml
	android:versionName
		INJECTED from D:\PROGRAMMING\AC-FIT\acfit_app\android\app\src\main\AndroidManifest.xml
	android:versionCode
		INJECTED from D:\PROGRAMMING\AC-FIT\acfit_app\android\app\src\main\AndroidManifest.xml
	xmlns:android
		ADDED from D:\PROGRAMMING\AC-FIT\acfit_app\android\app\src\main\AndroidManifest.xml:1:11-69
uses-permission#android.permission.INTERNET
ADDED from D:\PROGRAMMING\AC-FIT\acfit_app\android\app\src\main\AndroidManifest.xml:3:5-67
	android:name
		ADDED from D:\PROGRAMMING\AC-FIT\acfit_app\android\app\src\main\AndroidManifest.xml:3:22-64
queries
ADDED from D:\PROGRAMMING\AC-FIT\acfit_app\android\app\src\main\AndroidManifest.xml:43:5-60:15
intent#action:name:android.intent.action.PROCESS_TEXT+data:mimeType:text/plain
ADDED from D:\PROGRAMMING\AC-FIT\acfit_app\android\app\src\main\AndroidManifest.xml:44:9-47:18
action#android.intent.action.PROCESS_TEXT
ADDED from D:\PROGRAMMING\AC-FIT\acfit_app\android\app\src\main\AndroidManifest.xml:45:13-72
	android:name
		ADDED from D:\PROGRAMMING\AC-FIT\acfit_app\android\app\src\main\AndroidManifest.xml:45:21-70
data
ADDED from D:\PROGRAMMING\AC-FIT\acfit_app\android\app\src\main\AndroidManifest.xml:46:13-50
	android:scheme
		ADDED from D:\PROGRAMMING\AC-FIT\acfit_app\android\app\src\main\AndroidManifest.xml:52:19-41
	android:mimeType
		ADDED from D:\PROGRAMMING\AC-FIT\acfit_app\android\app\src\main\AndroidManifest.xml:46:19-48
intent#action:name:android.intent.action.VIEW+category:name:android.intent.category.BROWSABLE+data:scheme:https
ADDED from D:\PROGRAMMING\AC-FIT\acfit_app\android\app\src\main\AndroidManifest.xml:49:9-53:18
action#android.intent.action.VIEW
ADDED from D:\PROGRAMMING\AC-FIT\acfit_app\android\app\src\main\AndroidManifest.xml:50:13-65
	android:name
		ADDED from D:\PROGRAMMING\AC-FIT\acfit_app\android\app\src\main\AndroidManifest.xml:50:21-62
category#android.intent.category.BROWSABLE
ADDED from D:\PROGRAMMING\AC-FIT\acfit_app\android\app\src\main\AndroidManifest.xml:51:13-74
	android:name
		ADDED from D:\PROGRAMMING\AC-FIT\acfit_app\android\app\src\main\AndroidManifest.xml:51:23-71
intent#action:name:android.intent.action.VIEW+category:name:android.intent.category.BROWSABLE+data:scheme:http
ADDED from D:\PROGRAMMING\AC-FIT\acfit_app\android\app\src\main\AndroidManifest.xml:55:9-59:18
activity#com.example.acfit_app.MainActivity
ADDED from D:\PROGRAMMING\AC-FIT\acfit_app\android\app\src\main\AndroidManifest.xml:10:9-31:20
	android:launchMode
		ADDED from D:\PROGRAMMING\AC-FIT\acfit_app\android\app\src\main\AndroidManifest.xml:13:13-43
	android:hardwareAccelerated
		ADDED from D:\PROGRAMMING\AC-FIT\acfit_app\android\app\src\main\AndroidManifest.xml:17:13-47
	android:windowSoftInputMode
		ADDED from D:\PROGRAMMING\AC-FIT\acfit_app\android\app\src\main\AndroidManifest.xml:18:13-55
	android:exported
		ADDED from D:\PROGRAMMING\AC-FIT\acfit_app\android\app\src\main\AndroidManifest.xml:12:13-36
	android:configChanges
		ADDED from D:\PROGRAMMING\AC-FIT\acfit_app\android\app\src\main\AndroidManifest.xml:16:13-163
	android:theme
		ADDED from D:\PROGRAMMING\AC-FIT\acfit_app\android\app\src\main\AndroidManifest.xml:15:13-47
	android:taskAffinity
		ADDED from D:\PROGRAMMING\AC-FIT\acfit_app\android\app\src\main\AndroidManifest.xml:14:13-36
	android:name
		ADDED from D:\PROGRAMMING\AC-FIT\acfit_app\android\app\src\main\AndroidManifest.xml:11:13-41
meta-data#io.flutter.embedding.android.NormalTheme
ADDED from D:\PROGRAMMING\AC-FIT\acfit_app\android\app\src\main\AndroidManifest.xml:23:13-26:17
	android:resource
		ADDED from D:\PROGRAMMING\AC-FIT\acfit_app\android\app\src\main\AndroidManifest.xml:25:15-52
	android:name
		ADDED from D:\PROGRAMMING\AC-FIT\acfit_app\android\app\src\main\AndroidManifest.xml:24:15-70
intent-filter#action:name:android.intent.action.MAIN+category:name:android.intent.category.LAUNCHER
ADDED from D:\PROGRAMMING\AC-FIT\acfit_app\android\app\src\main\AndroidManifest.xml:27:13-30:29
action#android.intent.action.MAIN
ADDED from D:\PROGRAMMING\AC-FIT\acfit_app\android\app\src\main\AndroidManifest.xml:28:17-68
	android:name
		ADDED from D:\PROGRAMMING\AC-FIT\acfit_app\android\app\src\main\AndroidManifest.xml:28:25-66
category#android.intent.category.LAUNCHER
ADDED from D:\PROGRAMMING\AC-FIT\acfit_app\android\app\src\main\AndroidManifest.xml:29:17-76
	android:name
		ADDED from D:\PROGRAMMING\AC-FIT\acfit_app\android\app\src\main\AndroidManifest.xml:29:27-74
meta-data#flutterEmbedding
ADDED from D:\PROGRAMMING\AC-FIT\acfit_app\android\app\src\main\AndroidManifest.xml:34:9-36:33
	android:value
		ADDED from D:\PROGRAMMING\AC-FIT\acfit_app\android\app\src\main\AndroidManifest.xml:36:13-30
	android:name
		ADDED from D:\PROGRAMMING\AC-FIT\acfit_app\android\app\src\main\AndroidManifest.xml:35:13-44
uses-sdk
INJECTED from D:\PROGRAMMING\AC-FIT\acfit_app\android\app\src\main\AndroidManifest.xml reason: use-sdk injection requested
INJECTED from D:\PROGRAMMING\AC-FIT\acfit_app\android\app\src\main\AndroidManifest.xml
INJECTED from D:\PROGRAMMING\AC-FIT\acfit_app\android\app\src\main\AndroidManifest.xml
MERGED from [:flutter_timezone] D:\PROGRAMMING\AC-FIT\acfit_app\build\flutter_timezone\intermediates\merged_manifest\release\processReleaseManifest\AndroidManifest.xml:5:5-44
MERGED from [:flutter_timezone] D:\PROGRAMMING\AC-FIT\acfit_app\build\flutter_timezone\intermediates\merged_manifest\release\processReleaseManifest\AndroidManifest.xml:5:5-44
MERGED from [:shared_preferences_android] D:\PROGRAMMING\AC-FIT\acfit_app\build\shared_preferences_android\intermediates\merged_manifest\release\processReleaseManifest\AndroidManifest.xml:5:5-44
MERGED from [:shared_preferences_android] D:\PROGRAMMING\AC-FIT\acfit_app\build\shared_preferences_android\intermediates\merged_manifest\release\processReleaseManifest\AndroidManifest.xml:5:5-44
MERGED from [:wakelock_plus] D:\PROGRAMMING\AC-FIT\acfit_app\build\wakelock_plus\intermediates\merged_manifest\release\processReleaseManifest\AndroidManifest.xml:5:5-44
MERGED from [:wakelock_plus] D:\PROGRAMMING\AC-FIT\acfit_app\build\wakelock_plus\intermediates\merged_manifest\release\processReleaseManifest\AndroidManifest.xml:5:5-44
MERGED from [:package_info_plus] D:\PROGRAMMING\AC-FIT\acfit_app\build\package_info_plus\intermediates\merged_manifest\release\processReleaseManifest\AndroidManifest.xml:5:5-44
MERGED from [:package_info_plus] D:\PROGRAMMING\AC-FIT\acfit_app\build\package_info_plus\intermediates\merged_manifest\release\processReleaseManifest\AndroidManifest.xml:5:5-44
MERGED from [:audioplayers_android] D:\PROGRAMMING\AC-FIT\acfit_app\build\audioplayers_android\intermediates\merged_manifest\release\processReleaseManifest\AndroidManifest.xml:5:5-44
MERGED from [:audioplayers_android] D:\PROGRAMMING\AC-FIT\acfit_app\build\audioplayers_android\intermediates\merged_manifest\release\processReleaseManifest\AndroidManifest.xml:5:5-44
MERGED from [:workmanager] D:\PROGRAMMING\AC-FIT\acfit_app\build\workmanager\intermediates\merged_manifest\release\processReleaseManifest\AndroidManifest.xml:5:5-44
MERGED from [:workmanager] D:\PROGRAMMING\AC-FIT\acfit_app\build\workmanager\intermediates\merged_manifest\release\processReleaseManifest\AndroidManifest.xml:5:5-44
MERGED from [:flutter_local_notifications] D:\PROGRAMMING\AC-FIT\acfit_app\build\flutter_local_notifications\intermediates\merged_manifest\release\processReleaseManifest\AndroidManifest.xml:5:5-44
MERGED from [:flutter_local_notifications] D:\PROGRAMMING\AC-FIT\acfit_app\build\flutter_local_notifications\intermediates\merged_manifest\release\processReleaseManifest\AndroidManifest.xml:5:5-44
MERGED from [:image_picker_android] D:\PROGRAMMING\AC-FIT\acfit_app\build\image_picker_android\intermediates\merged_manifest\release\processReleaseManifest\AndroidManifest.xml:6:5-44
MERGED from [:image_picker_android] D:\PROGRAMMING\AC-FIT\acfit_app\build\image_picker_android\intermediates\merged_manifest\release\processReleaseManifest\AndroidManifest.xml:6:5-44
MERGED from [:url_launcher_android] D:\PROGRAMMING\AC-FIT\acfit_app\build\url_launcher_android\intermediates\merged_manifest\release\processReleaseManifest\AndroidManifest.xml:5:5-44
MERGED from [:url_launcher_android] D:\PROGRAMMING\AC-FIT\acfit_app\build\url_launcher_android\intermediates\merged_manifest\release\processReleaseManifest\AndroidManifest.xml:5:5-44
MERGED from [:connectivity_plus] D:\PROGRAMMING\AC-FIT\acfit_app\build\connectivity_plus\intermediates\merged_manifest\release\processReleaseManifest\AndroidManifest.xml:5:5-44
MERGED from [:connectivity_plus] D:\PROGRAMMING\AC-FIT\acfit_app\build\connectivity_plus\intermediates\merged_manifest\release\processReleaseManifest\AndroidManifest.xml:5:5-44
MERGED from [:flutter_plugin_android_lifecycle] D:\PROGRAMMING\AC-FIT\acfit_app\build\flutter_plugin_android_lifecycle\intermediates\merged_manifest\release\processReleaseManifest\AndroidManifest.xml:5:5-44
MERGED from [:flutter_plugin_android_lifecycle] D:\PROGRAMMING\AC-FIT\acfit_app\build\flutter_plugin_android_lifecycle\intermediates\merged_manifest\release\processReleaseManifest\AndroidManifest.xml:5:5-44
MERGED from [:flutter_secure_storage] D:\PROGRAMMING\AC-FIT\acfit_app\build\flutter_secure_storage\intermediates\merged_manifest\release\processReleaseManifest\AndroidManifest.xml:6:5-8:53
MERGED from [:flutter_secure_storage] D:\PROGRAMMING\AC-FIT\acfit_app\build\flutter_secure_storage\intermediates\merged_manifest\release\processReleaseManifest\AndroidManifest.xml:6:5-8:53
MERGED from [:path_provider_android] D:\PROGRAMMING\AC-FIT\acfit_app\build\path_provider_android\intermediates\merged_manifest\release\processReleaseManifest\AndroidManifest.xml:5:5-44
MERGED from [:path_provider_android] D:\PROGRAMMING\AC-FIT\acfit_app\build\path_provider_android\intermediates\merged_manifest\release\processReleaseManifest\AndroidManifest.xml:5:5-44
MERGED from [:sqflite_android] D:\PROGRAMMING\AC-FIT\acfit_app\build\sqflite_android\intermediates\merged_manifest\release\processReleaseManifest\AndroidManifest.xml:5:5-44
MERGED from [:sqflite_android] D:\PROGRAMMING\AC-FIT\acfit_app\build\sqflite_android\intermediates\merged_manifest\release\processReleaseManifest\AndroidManifest.xml:5:5-44
MERGED from [:video_player_android] D:\PROGRAMMING\AC-FIT\acfit_app\build\video_player_android\intermediates\merged_manifest\release\processReleaseManifest\AndroidManifest.xml:5:5-44
MERGED from [:video_player_android] D:\PROGRAMMING\AC-FIT\acfit_app\build\video_player_android\intermediates\merged_manifest\release\processReleaseManifest\AndroidManifest.xml:5:5-44
MERGED from [androidx.media:media:1.1.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\9306062905f4d3cda1670c0e5f3df17e\transformed\media-1.1.0\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.media:media:1.1.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\9306062905f4d3cda1670c0e5f3df17e\transformed\media-1.1.0\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.preference:preference:1.2.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\0aea3dee6044b352a54f8fb9c4a12c07\transformed\preference-1.2.1\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.preference:preference:1.2.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\0aea3dee6044b352a54f8fb9c4a12c07\transformed\preference-1.2.1\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.appcompat:appcompat:1.1.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\32d7d149835c8c00f0697dfcab9adb84\transformed\appcompat-1.1.0\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.appcompat:appcompat:1.1.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\32d7d149835c8c00f0697dfcab9adb84\transformed\appcompat-1.1.0\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.fragment:fragment-ktx:1.7.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\dd8f2b9cf49e7284b6695565297622e3\transformed\jetified-fragment-ktx-1.7.1\AndroidManifest.xml:5:5-44
MERGED from [androidx.fragment:fragment-ktx:1.7.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\dd8f2b9cf49e7284b6695565297622e3\transformed\jetified-fragment-ktx-1.7.1\AndroidManifest.xml:5:5-44
MERGED from [androidx.fragment:fragment:1.7.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\812b0debb255957f3fcc9a2669f27c22\transformed\fragment-1.7.1\AndroidManifest.xml:5:5-44
MERGED from [androidx.fragment:fragment:1.7.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\812b0debb255957f3fcc9a2669f27c22\transformed\fragment-1.7.1\AndroidManifest.xml:5:5-44
MERGED from [androidx.activity:activity-ktx:1.9.3] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\4d0eff89ceab5cf34de5203e536a9f16\transformed\jetified-activity-ktx-1.9.3\AndroidManifest.xml:5:5-44
MERGED from [androidx.activity:activity-ktx:1.9.3] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\4d0eff89ceab5cf34de5203e536a9f16\transformed\jetified-activity-ktx-1.9.3\AndroidManifest.xml:5:5-44
MERGED from [androidx.activity:activity:1.9.3] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\e57d2fe08522bc82b0ac6f830a2f178d\transformed\jetified-activity-1.9.3\AndroidManifest.xml:20:5-44
MERGED from [androidx.activity:activity:1.9.3] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\e57d2fe08522bc82b0ac6f830a2f178d\transformed\jetified-activity-1.9.3\AndroidManifest.xml:20:5-44
MERGED from [androidx.browser:browser:1.8.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\5683f3a75f4aae74864d56d25d09ab49\transformed\browser-1.8.0\AndroidManifest.xml:5:5-44
MERGED from [androidx.browser:browser:1.8.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\5683f3a75f4aae74864d56d25d09ab49\transformed\browser-1.8.0\AndroidManifest.xml:5:5-44
MERGED from [androidx.media3:media3-extractor:1.4.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\0507ffafbde2f9a8ad624d19df0920ce\transformed\jetified-media3-extractor-1.4.1\AndroidManifest.xml:20:5-44
MERGED from [androidx.media3:media3-extractor:1.4.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\0507ffafbde2f9a8ad624d19df0920ce\transformed\jetified-media3-extractor-1.4.1\AndroidManifest.xml:20:5-44
MERGED from [androidx.media3:media3-container:1.4.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\d0e1ce7a6f4fbc4be2e75726209a7737\transformed\jetified-media3-container-1.4.1\AndroidManifest.xml:20:5-44
MERGED from [androidx.media3:media3-container:1.4.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\d0e1ce7a6f4fbc4be2e75726209a7737\transformed\jetified-media3-container-1.4.1\AndroidManifest.xml:20:5-44
MERGED from [androidx.media3:media3-datasource:1.4.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\1662d0a82ced6df7352db1ad102f719f\transformed\jetified-media3-datasource-1.4.1\AndroidManifest.xml:20:5-44
MERGED from [androidx.media3:media3-datasource:1.4.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\1662d0a82ced6df7352db1ad102f719f\transformed\jetified-media3-datasource-1.4.1\AndroidManifest.xml:20:5-44
MERGED from [androidx.media3:media3-decoder:1.4.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\7ba5ac15d51f0344948a46db4a42d918\transformed\jetified-media3-decoder-1.4.1\AndroidManifest.xml:20:5-44
MERGED from [androidx.media3:media3-decoder:1.4.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\7ba5ac15d51f0344948a46db4a42d918\transformed\jetified-media3-decoder-1.4.1\AndroidManifest.xml:20:5-44
MERGED from [androidx.media3:media3-database:1.4.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\e708d741ac9b6c15a552df6302769687\transformed\jetified-media3-database-1.4.1\AndroidManifest.xml:20:5-44
MERGED from [androidx.media3:media3-database:1.4.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\e708d741ac9b6c15a552df6302769687\transformed\jetified-media3-database-1.4.1\AndroidManifest.xml:20:5-44
MERGED from [androidx.media3:media3-common:1.4.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\0bdc39bce25c90ffaf84752415c05baa\transformed\jetified-media3-common-1.4.1\AndroidManifest.xml:20:5-44
MERGED from [androidx.media3:media3-common:1.4.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\0bdc39bce25c90ffaf84752415c05baa\transformed\jetified-media3-common-1.4.1\AndroidManifest.xml:20:5-44
MERGED from [androidx.media3:media3-exoplayer-hls:1.4.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\7d358ae40d5f7529b531bcb8822e904a\transformed\jetified-media3-exoplayer-hls-1.4.1\AndroidManifest.xml:20:5-44
MERGED from [androidx.media3:media3-exoplayer-hls:1.4.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\7d358ae40d5f7529b531bcb8822e904a\transformed\jetified-media3-exoplayer-hls-1.4.1\AndroidManifest.xml:20:5-44
MERGED from [androidx.media3:media3-exoplayer-dash:1.4.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\5416cf3532cb6ded6f8189dbf43ae176\transformed\jetified-media3-exoplayer-dash-1.4.1\AndroidManifest.xml:20:5-44
MERGED from [androidx.media3:media3-exoplayer-dash:1.4.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\5416cf3532cb6ded6f8189dbf43ae176\transformed\jetified-media3-exoplayer-dash-1.4.1\AndroidManifest.xml:20:5-44
MERGED from [androidx.media3:media3-exoplayer-rtsp:1.4.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\9c52213adb527094bb6979d4146926a6\transformed\jetified-media3-exoplayer-rtsp-1.4.1\AndroidManifest.xml:20:5-44
MERGED from [androidx.media3:media3-exoplayer-rtsp:1.4.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\9c52213adb527094bb6979d4146926a6\transformed\jetified-media3-exoplayer-rtsp-1.4.1\AndroidManifest.xml:20:5-44
MERGED from [androidx.media3:media3-exoplayer-smoothstreaming:1.4.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\442c4d5a4d19f6310c30b463bc3f448d\transformed\jetified-media3-exoplayer-smoothstreaming-1.4.1\AndroidManifest.xml:20:5-44
MERGED from [androidx.media3:media3-exoplayer-smoothstreaming:1.4.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\442c4d5a4d19f6310c30b463bc3f448d\transformed\jetified-media3-exoplayer-smoothstreaming-1.4.1\AndroidManifest.xml:20:5-44
MERGED from [androidx.media3:media3-exoplayer:1.4.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\f3900c31e436e087be46264d65f1dea9\transformed\jetified-media3-exoplayer-1.4.1\AndroidManifest.xml:20:5-44
MERGED from [androidx.media3:media3-exoplayer:1.4.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\f3900c31e436e087be46264d65f1dea9\transformed\jetified-media3-exoplayer-1.4.1\AndroidManifest.xml:20:5-44
MERGED from [androidx.work:work-runtime:2.8.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\8975063235ea0ba114f3bb0240765838\transformed\work-runtime-2.8.1\AndroidManifest.xml:21:5-44
MERGED from [androidx.work:work-runtime:2.8.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\8975063235ea0ba114f3bb0240765838\transformed\work-runtime-2.8.1\AndroidManifest.xml:21:5-44
MERGED from [androidx.recyclerview:recyclerview:1.0.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\b2df645d79e711076ea45b6a8385e99c\transformed\recyclerview-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.recyclerview:recyclerview:1.0.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\b2df645d79e711076ea45b6a8385e99c\transformed\recyclerview-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.legacy:legacy-support-core-ui:1.0.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\6fb5bbbc1627b43bb066b846f50b0b9c\transformed\legacy-support-core-ui-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.legacy:legacy-support-core-ui:1.0.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\6fb5bbbc1627b43bb066b846f50b0b9c\transformed\legacy-support-core-ui-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.slidingpanelayout:slidingpanelayout:1.2.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\f3ef3caec9d424994a2628e26767d1d4\transformed\slidingpanelayout-1.2.0\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.slidingpanelayout:slidingpanelayout:1.2.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\f3ef3caec9d424994a2628e26767d1d4\transformed\slidingpanelayout-1.2.0\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.window:window:1.2.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\970764a81dd610c772147726baa7dca5\transformed\jetified-window-1.2.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.window:window:1.2.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\970764a81dd610c772147726baa7dca5\transformed\jetified-window-1.2.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.window:window-java:1.2.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\2a5284c6eeabc2d8b6190c9576956065\transformed\jetified-window-java-1.2.0\AndroidManifest.xml:19:5-44
MERGED from [androidx.window:window-java:1.2.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\2a5284c6eeabc2d8b6190c9576956065\transformed\jetified-window-java-1.2.0\AndroidManifest.xml:19:5-44
MERGED from [androidx.legacy:legacy-support-core-utils:1.0.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\4c4a6ad3f7b10ac103c0346368799501\transformed\legacy-support-core-utils-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.legacy:legacy-support-core-utils:1.0.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\4c4a6ad3f7b10ac103c0346368799501\transformed\legacy-support-core-utils-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.loader:loader:1.0.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\cf593104563b66144c3b6dc7fc4802c1\transformed\loader-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.loader:loader:1.0.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\cf593104563b66144c3b6dc7fc4802c1\transformed\loader-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.lifecycle:lifecycle-livedata-core-ktx:2.7.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\645c56368631721d8b7755f7e8a726a8\transformed\jetified-lifecycle-livedata-core-ktx-2.7.0\AndroidManifest.xml:5:5-44
MERGED from [androidx.lifecycle:lifecycle-livedata-core-ktx:2.7.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\645c56368631721d8b7755f7e8a726a8\transformed\jetified-lifecycle-livedata-core-ktx-2.7.0\AndroidManifest.xml:5:5-44
MERGED from [androidx.lifecycle:lifecycle-viewmodel-ktx:2.7.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\d8c201c583c6fe469398993e61fede1c\transformed\jetified-lifecycle-viewmodel-ktx-2.7.0\AndroidManifest.xml:5:5-44
MERGED from [androidx.lifecycle:lifecycle-viewmodel-ktx:2.7.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\d8c201c583c6fe469398993e61fede1c\transformed\jetified-lifecycle-viewmodel-ktx-2.7.0\AndroidManifest.xml:5:5-44
MERGED from [androidx.lifecycle:lifecycle-runtime-ktx:2.7.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\3ff6adcf87eb4959e12e2c03b49ab87a\transformed\jetified-lifecycle-runtime-ktx-2.7.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.lifecycle:lifecycle-runtime-ktx:2.7.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\3ff6adcf87eb4959e12e2c03b49ab87a\transformed\jetified-lifecycle-runtime-ktx-2.7.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.lifecycle:lifecycle-livedata-core:2.7.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\07de910142823f029a6feac220e8ca4a\transformed\lifecycle-livedata-core-2.7.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.lifecycle:lifecycle-livedata-core:2.7.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\07de910142823f029a6feac220e8ca4a\transformed\lifecycle-livedata-core-2.7.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.lifecycle:lifecycle-viewmodel:2.7.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\594b8e088512a24631e5a37668efcdba\transformed\lifecycle-viewmodel-2.7.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.lifecycle:lifecycle-viewmodel:2.7.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\594b8e088512a24631e5a37668efcdba\transformed\lifecycle-viewmodel-2.7.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.lifecycle:lifecycle-service:2.7.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\e18fb94549ccb4f6a794ad42ccd1ec5d\transformed\jetified-lifecycle-service-2.7.0\AndroidManifest.xml:5:5-44
MERGED from [androidx.lifecycle:lifecycle-service:2.7.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\e18fb94549ccb4f6a794ad42ccd1ec5d\transformed\jetified-lifecycle-service-2.7.0\AndroidManifest.xml:5:5-44
MERGED from [androidx.lifecycle:lifecycle-livedata:2.7.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\7c8820905eb370fbd3bb42d26ba8ae5a\transformed\lifecycle-livedata-2.7.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.lifecycle:lifecycle-livedata:2.7.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\7c8820905eb370fbd3bb42d26ba8ae5a\transformed\lifecycle-livedata-2.7.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.lifecycle:lifecycle-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\3406eb4cffe3e53bb6d20996e0b4ec87\transformed\lifecycle-runtime-2.7.0\AndroidManifest.xml:5:5-44
MERGED from [androidx.lifecycle:lifecycle-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\3406eb4cffe3e53bb6d20996e0b4ec87\transformed\lifecycle-runtime-2.7.0\AndroidManifest.xml:5:5-44
MERGED from [androidx.lifecycle:lifecycle-process:2.7.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\e920d985392df03e5b02e06877a52d21\transformed\jetified-lifecycle-process-2.7.0\AndroidManifest.xml:21:5-44
MERGED from [androidx.lifecycle:lifecycle-process:2.7.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\e920d985392df03e5b02e06877a52d21\transformed\jetified-lifecycle-process-2.7.0\AndroidManifest.xml:21:5-44
MERGED from [androidx.savedstate:savedstate-ktx:1.2.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\7f3b092f04c0253e979c83421f120f88\transformed\jetified-savedstate-ktx-1.2.1\AndroidManifest.xml:20:5-44
MERGED from [androidx.savedstate:savedstate-ktx:1.2.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\7f3b092f04c0253e979c83421f120f88\transformed\jetified-savedstate-ktx-1.2.1\AndroidManifest.xml:20:5-44
MERGED from [androidx.savedstate:savedstate:1.2.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\ae5f57bd372fc0b34c3bf33092f8edd1\transformed\jetified-savedstate-1.2.1\AndroidManifest.xml:20:5-44
MERGED from [androidx.savedstate:savedstate:1.2.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\ae5f57bd372fc0b34c3bf33092f8edd1\transformed\jetified-savedstate-1.2.1\AndroidManifest.xml:20:5-44
MERGED from [androidx.lifecycle:lifecycle-viewmodel-savedstate:2.7.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\834fbfef458f2f76792d20bc77011687\transformed\jetified-lifecycle-viewmodel-savedstate-2.7.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.lifecycle:lifecycle-viewmodel-savedstate:2.7.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\834fbfef458f2f76792d20bc77011687\transformed\jetified-lifecycle-viewmodel-savedstate-2.7.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.core:core-ktx:1.13.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\538273fdc918a79c3b54859282434b2c\transformed\jetified-core-ktx-1.13.1\AndroidManifest.xml:5:5-44
MERGED from [androidx.core:core-ktx:1.13.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\538273fdc918a79c3b54859282434b2c\transformed\jetified-core-ktx-1.13.1\AndroidManifest.xml:5:5-44
MERGED from [androidx.viewpager:viewpager:1.0.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\e71467fe7caeb25f99b2fabbeba6edee\transformed\viewpager-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.viewpager:viewpager:1.0.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\e71467fe7caeb25f99b2fabbeba6edee\transformed\viewpager-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.appcompat:appcompat-resources:1.1.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\1ff8cdab1140e9ed76ee2cc1c84e5b27\transformed\jetified-appcompat-resources-1.1.0\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.appcompat:appcompat-resources:1.1.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\1ff8cdab1140e9ed76ee2cc1c84e5b27\transformed\jetified-appcompat-resources-1.1.0\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.drawerlayout:drawerlayout:1.0.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\9108df622b0b8e8dbba78b566c3bdc67\transformed\drawerlayout-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.drawerlayout:drawerlayout:1.0.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\9108df622b0b8e8dbba78b566c3bdc67\transformed\drawerlayout-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.coordinatorlayout:coordinatorlayout:1.0.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\b6435e177046c519b4e9a09d53555b3f\transformed\coordinatorlayout-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.coordinatorlayout:coordinatorlayout:1.0.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\b6435e177046c519b4e9a09d53555b3f\transformed\coordinatorlayout-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.customview:customview:1.1.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\348e9995eb75a687bf31011ee5dfb249\transformed\customview-1.1.0\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.customview:customview:1.1.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\348e9995eb75a687bf31011ee5dfb249\transformed\customview-1.1.0\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.transition:transition:1.4.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\3e9c3e49153aa47f3a49791ab647f926\transformed\transition-1.4.1\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.transition:transition:1.4.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\3e9c3e49153aa47f3a49791ab647f926\transformed\transition-1.4.1\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.vectordrawable:vectordrawable-animated:1.1.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\0fa1677c034ef049b8c12b4202dcc8c4\transformed\vectordrawable-animated-1.1.0\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.vectordrawable:vectordrawable-animated:1.1.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\0fa1677c034ef049b8c12b4202dcc8c4\transformed\vectordrawable-animated-1.1.0\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.vectordrawable:vectordrawable:1.1.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\2111a692760d2213c419f21507346c83\transformed\vectordrawable-1.1.0\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.vectordrawable:vectordrawable:1.1.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\2111a692760d2213c419f21507346c83\transformed\vectordrawable-1.1.0\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.swiperefreshlayout:swiperefreshlayout:1.0.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\bbe59546d7834ae0953d7d1775d43fac\transformed\swiperefreshlayout-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.swiperefreshlayout:swiperefreshlayout:1.0.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\bbe59546d7834ae0953d7d1775d43fac\transformed\swiperefreshlayout-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.asynclayoutinflater:asynclayoutinflater:1.0.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\7295a6e9f96a0daeed321ae2faac1f7b\transformed\asynclayoutinflater-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.asynclayoutinflater:asynclayoutinflater:1.0.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\7295a6e9f96a0daeed321ae2faac1f7b\transformed\asynclayoutinflater-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.core:core:1.13.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\deb838f5c4813d0c441214e1ddf497cc\transformed\core-1.13.1\AndroidManifest.xml:20:5-44
MERGED from [androidx.core:core:1.13.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\deb838f5c4813d0c441214e1ddf497cc\transformed\core-1.13.1\AndroidManifest.xml:20:5-44
MERGED from [androidx.security:security-crypto:1.1.0-alpha06] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\aefd614a712efdaf0e306e9a9f70c30d\transformed\jetified-security-crypto-1.1.0-alpha06\AndroidManifest.xml:20:5-44
MERGED from [androidx.security:security-crypto:1.1.0-alpha06] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\aefd614a712efdaf0e306e9a9f70c30d\transformed\jetified-security-crypto-1.1.0-alpha06\AndroidManifest.xml:20:5-44
MERGED from [androidx.exifinterface:exifinterface:1.3.7] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\747df56e3edd1e12435e5eae9046f54c\transformed\exifinterface-1.3.7\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.exifinterface:exifinterface:1.3.7] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\747df56e3edd1e12435e5eae9046f54c\transformed\exifinterface-1.3.7\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\eabc1325411a824ae74b9b450245750e\transformed\jetified-profileinstaller-1.3.1\AndroidManifest.xml:21:5-44
MERGED from [androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\eabc1325411a824ae74b9b450245750e\transformed\jetified-profileinstaller-1.3.1\AndroidManifest.xml:21:5-44
MERGED from [androidx.startup:startup-runtime:1.1.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\77d6589d54f70503af1ad7bd5c95eb07\transformed\jetified-startup-runtime-1.1.1\AndroidManifest.xml:21:5-23:41
MERGED from [androidx.startup:startup-runtime:1.1.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\77d6589d54f70503af1ad7bd5c95eb07\transformed\jetified-startup-runtime-1.1.1\AndroidManifest.xml:21:5-23:41
MERGED from [androidx.tracing:tracing:1.2.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\67e6cf8dd6496a1e1ab42bfbc69e3371\transformed\jetified-tracing-1.2.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.tracing:tracing:1.2.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\67e6cf8dd6496a1e1ab42bfbc69e3371\transformed\jetified-tracing-1.2.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.interpolator:interpolator:1.0.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\7620a15c947fbbae2a35989fac4ced29\transformed\interpolator-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.interpolator:interpolator:1.0.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\7620a15c947fbbae2a35989fac4ced29\transformed\interpolator-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.versionedparcelable:versionedparcelable:1.1.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\ae03ddb689964a197cc9572a48c09923\transformed\versionedparcelable-1.1.1\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.versionedparcelable:versionedparcelable:1.1.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\ae03ddb689964a197cc9572a48c09923\transformed\versionedparcelable-1.1.1\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.datastore:datastore-core-android:1.1.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\7c345815c222fade1dd4a11ff4988234\transformed\jetified-datastore-core-release\AndroidManifest.xml:5:5-44
MERGED from [androidx.datastore:datastore-core-android:1.1.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\7c345815c222fade1dd4a11ff4988234\transformed\jetified-datastore-core-release\AndroidManifest.xml:5:5-44
MERGED from [androidx.datastore:datastore-preferences-android:1.1.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\416d2416b9816fecc86610fc0bc7c7ac\transformed\jetified-datastore-preferences-release\AndroidManifest.xml:20:5-44
MERGED from [androidx.datastore:datastore-preferences-android:1.1.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\416d2416b9816fecc86610fc0bc7c7ac\transformed\jetified-datastore-preferences-release\AndroidManifest.xml:20:5-44
MERGED from [androidx.datastore:datastore-android:1.1.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\e97941c4f2767d0e57cf200fdcb26d11\transformed\jetified-datastore-release\AndroidManifest.xml:20:5-44
MERGED from [androidx.datastore:datastore-android:1.1.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\e97941c4f2767d0e57cf200fdcb26d11\transformed\jetified-datastore-release\AndroidManifest.xml:20:5-44
MERGED from [androidx.room:room-runtime:2.5.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\b0a143534f1d9e3ab757fbcdd209cfbb\transformed\room-runtime-2.5.0\AndroidManifest.xml:21:5-44
MERGED from [androidx.room:room-runtime:2.5.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\b0a143534f1d9e3ab757fbcdd209cfbb\transformed\room-runtime-2.5.0\AndroidManifest.xml:21:5-44
MERGED from [androidx.sqlite:sqlite-framework:2.3.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\584ffc75cc83b4f6d7ebd88158cc055e\transformed\sqlite-framework-2.3.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.sqlite:sqlite-framework:2.3.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\584ffc75cc83b4f6d7ebd88158cc055e\transformed\sqlite-framework-2.3.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.arch.core:core-runtime:2.2.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\24b2d70c983456e4678be6d7afa07780\transformed\core-runtime-2.2.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.arch.core:core-runtime:2.2.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\24b2d70c983456e4678be6d7afa07780\transformed\core-runtime-2.2.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.cursoradapter:cursoradapter:1.0.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\cdf4cc3b708776cdc2805e90540cbeb3\transformed\cursoradapter-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.cursoradapter:cursoradapter:1.0.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\cdf4cc3b708776cdc2805e90540cbeb3\transformed\cursoradapter-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.sqlite:sqlite:2.3.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\e4002a4a3b92ef231a557362b3b46ea2\transformed\sqlite-2.3.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.sqlite:sqlite:2.3.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\e4002a4a3b92ef231a557362b3b46ea2\transformed\sqlite-2.3.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.window.extensions.core:core:1.0.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\55a192cd1e8c493370fa6465d68c1145\transformed\jetified-core-1.0.0\AndroidManifest.xml:5:5-44
MERGED from [androidx.window.extensions.core:core:1.0.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\55a192cd1e8c493370fa6465d68c1145\transformed\jetified-core-1.0.0\AndroidManifest.xml:5:5-44
MERGED from [androidx.documentfile:documentfile:1.0.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\38895ff92e9ea1cab9c97b52c490e983\transformed\documentfile-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.documentfile:documentfile:1.0.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\38895ff92e9ea1cab9c97b52c490e983\transformed\documentfile-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.localbroadcastmanager:localbroadcastmanager:1.0.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\14a7b710af8666cefae7796bd89d044c\transformed\localbroadcastmanager-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.localbroadcastmanager:localbroadcastmanager:1.0.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\14a7b710af8666cefae7796bd89d044c\transformed\localbroadcastmanager-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.print:print:1.0.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\adc76dc2b16c544bb53f946048eea1be\transformed\print-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.print:print:1.0.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\adc76dc2b16c544bb53f946048eea1be\transformed\print-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.annotation:annotation-experimental:1.4.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\99ae6e67fc3e457914a47c035e30c12a\transformed\jetified-annotation-experimental-1.4.0\AndroidManifest.xml:5:5-44
MERGED from [androidx.annotation:annotation-experimental:1.4.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\99ae6e67fc3e457914a47c035e30c12a\transformed\jetified-annotation-experimental-1.4.0\AndroidManifest.xml:5:5-44
MERGED from [com.google.android.play:core:1.10.3] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\9b10a4e78f0e7750b24f9632f5e597a2\transformed\jetified-core-1.10.3\AndroidManifest.xml:7:5-9:41
MERGED from [com.google.android.play:core:1.10.3] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\9b10a4e78f0e7750b24f9632f5e597a2\transformed\jetified-core-1.10.3\AndroidManifest.xml:7:5-9:41
MERGED from [com.getkeepsafe.relinker:relinker:1.4.5] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\6a9ec07ac357bc15a8d85f12e6d4189c\transformed\jetified-relinker-1.4.5\AndroidManifest.xml:5:5-43
MERGED from [com.getkeepsafe.relinker:relinker:1.4.5] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\6a9ec07ac357bc15a8d85f12e6d4189c\transformed\jetified-relinker-1.4.5\AndroidManifest.xml:5:5-43
	tools:overrideLibrary
		ADDED from [:flutter_secure_storage] D:\PROGRAMMING\AC-FIT\acfit_app\build\flutter_secure_storage\intermediates\merged_manifest\release\processReleaseManifest\AndroidManifest.xml:8:9-50
	android:targetSdkVersion
		INJECTED from D:\PROGRAMMING\AC-FIT\acfit_app\android\app\src\main\AndroidManifest.xml
	android:minSdkVersion
		INJECTED from D:\PROGRAMMING\AC-FIT\acfit_app\android\app\src\main\AndroidManifest.xml
uses-permission#android.permission.VIBRATE
ADDED from [:flutter_local_notifications] D:\PROGRAMMING\AC-FIT\acfit_app\build\flutter_local_notifications\intermediates\merged_manifest\release\processReleaseManifest\AndroidManifest.xml:7:5-66
	android:name
		ADDED from [:flutter_local_notifications] D:\PROGRAMMING\AC-FIT\acfit_app\build\flutter_local_notifications\intermediates\merged_manifest\release\processReleaseManifest\AndroidManifest.xml:7:22-63
uses-permission#android.permission.POST_NOTIFICATIONS
ADDED from [:flutter_local_notifications] D:\PROGRAMMING\AC-FIT\acfit_app\build\flutter_local_notifications\intermediates\merged_manifest\release\processReleaseManifest\AndroidManifest.xml:8:5-77
	android:name
		ADDED from [:flutter_local_notifications] D:\PROGRAMMING\AC-FIT\acfit_app\build\flutter_local_notifications\intermediates\merged_manifest\release\processReleaseManifest\AndroidManifest.xml:8:22-74
provider#io.flutter.plugins.imagepicker.ImagePickerFileProvider
ADDED from [:image_picker_android] D:\PROGRAMMING\AC-FIT\acfit_app\build\image_picker_android\intermediates\merged_manifest\release\processReleaseManifest\AndroidManifest.xml:9:9-17:20
	android:grantUriPermissions
		ADDED from [:image_picker_android] D:\PROGRAMMING\AC-FIT\acfit_app\build\image_picker_android\intermediates\merged_manifest\release\processReleaseManifest\AndroidManifest.xml:13:13-47
	android:authorities
		ADDED from [:image_picker_android] D:\PROGRAMMING\AC-FIT\acfit_app\build\image_picker_android\intermediates\merged_manifest\release\processReleaseManifest\AndroidManifest.xml:11:13-74
	android:exported
		ADDED from [:image_picker_android] D:\PROGRAMMING\AC-FIT\acfit_app\build\image_picker_android\intermediates\merged_manifest\release\processReleaseManifest\AndroidManifest.xml:12:13-37
	android:name
		ADDED from [:image_picker_android] D:\PROGRAMMING\AC-FIT\acfit_app\build\image_picker_android\intermediates\merged_manifest\release\processReleaseManifest\AndroidManifest.xml:10:13-82
meta-data#android.support.FILE_PROVIDER_PATHS
ADDED from [:image_picker_android] D:\PROGRAMMING\AC-FIT\acfit_app\build\image_picker_android\intermediates\merged_manifest\release\processReleaseManifest\AndroidManifest.xml:14:13-16:75
	android:resource
		ADDED from [:image_picker_android] D:\PROGRAMMING\AC-FIT\acfit_app\build\image_picker_android\intermediates\merged_manifest\release\processReleaseManifest\AndroidManifest.xml:16:17-72
	android:name
		ADDED from [:image_picker_android] D:\PROGRAMMING\AC-FIT\acfit_app\build\image_picker_android\intermediates\merged_manifest\release\processReleaseManifest\AndroidManifest.xml:15:17-67
service#com.google.android.gms.metadata.ModuleDependencies
ADDED from [:image_picker_android] D:\PROGRAMMING\AC-FIT\acfit_app\build\image_picker_android\intermediates\merged_manifest\release\processReleaseManifest\AndroidManifest.xml:19:9-31:19
	android:enabled
		ADDED from [:image_picker_android] D:\PROGRAMMING\AC-FIT\acfit_app\build\image_picker_android\intermediates\merged_manifest\release\processReleaseManifest\AndroidManifest.xml:21:13-36
	android:exported
		ADDED from [:image_picker_android] D:\PROGRAMMING\AC-FIT\acfit_app\build\image_picker_android\intermediates\merged_manifest\release\processReleaseManifest\AndroidManifest.xml:22:13-37
	tools:ignore
		ADDED from [:image_picker_android] D:\PROGRAMMING\AC-FIT\acfit_app\build\image_picker_android\intermediates\merged_manifest\release\processReleaseManifest\AndroidManifest.xml:23:13-40
	android:name
		ADDED from [:image_picker_android] D:\PROGRAMMING\AC-FIT\acfit_app\build\image_picker_android\intermediates\merged_manifest\release\processReleaseManifest\AndroidManifest.xml:20:13-78
intent-filter#action:name:com.google.android.gms.metadata.MODULE_DEPENDENCIES
ADDED from [:image_picker_android] D:\PROGRAMMING\AC-FIT\acfit_app\build\image_picker_android\intermediates\merged_manifest\release\processReleaseManifest\AndroidManifest.xml:24:13-26:29
action#com.google.android.gms.metadata.MODULE_DEPENDENCIES
ADDED from [:image_picker_android] D:\PROGRAMMING\AC-FIT\acfit_app\build\image_picker_android\intermediates\merged_manifest\release\processReleaseManifest\AndroidManifest.xml:25:17-94
	android:name
		ADDED from [:image_picker_android] D:\PROGRAMMING\AC-FIT\acfit_app\build\image_picker_android\intermediates\merged_manifest\release\processReleaseManifest\AndroidManifest.xml:25:25-91
meta-data#photopicker_activity:0:required
ADDED from [:image_picker_android] D:\PROGRAMMING\AC-FIT\acfit_app\build\image_picker_android\intermediates\merged_manifest\release\processReleaseManifest\AndroidManifest.xml:28:13-30:36
	android:value
		ADDED from [:image_picker_android] D:\PROGRAMMING\AC-FIT\acfit_app\build\image_picker_android\intermediates\merged_manifest\release\processReleaseManifest\AndroidManifest.xml:30:17-33
	android:name
		ADDED from [:image_picker_android] D:\PROGRAMMING\AC-FIT\acfit_app\build\image_picker_android\intermediates\merged_manifest\release\processReleaseManifest\AndroidManifest.xml:29:17-63
activity#io.flutter.plugins.urllauncher.WebViewActivity
ADDED from [:url_launcher_android] D:\PROGRAMMING\AC-FIT\acfit_app\build\url_launcher_android\intermediates\merged_manifest\release\processReleaseManifest\AndroidManifest.xml:8:9-11:74
	android:exported
		ADDED from [:url_launcher_android] D:\PROGRAMMING\AC-FIT\acfit_app\build\url_launcher_android\intermediates\merged_manifest\release\processReleaseManifest\AndroidManifest.xml:10:13-37
	android:theme
		ADDED from [:url_launcher_android] D:\PROGRAMMING\AC-FIT\acfit_app\build\url_launcher_android\intermediates\merged_manifest\release\processReleaseManifest\AndroidManifest.xml:11:13-71
	android:name
		ADDED from [:url_launcher_android] D:\PROGRAMMING\AC-FIT\acfit_app\build\url_launcher_android\intermediates\merged_manifest\release\processReleaseManifest\AndroidManifest.xml:9:13-74
uses-permission#android.permission.ACCESS_NETWORK_STATE
ADDED from [:connectivity_plus] D:\PROGRAMMING\AC-FIT\acfit_app\build\connectivity_plus\intermediates\merged_manifest\release\processReleaseManifest\AndroidManifest.xml:7:5-79
MERGED from [androidx.media3:media3-common:1.4.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\0bdc39bce25c90ffaf84752415c05baa\transformed\jetified-media3-common-1.4.1\AndroidManifest.xml:22:5-79
MERGED from [androidx.media3:media3-common:1.4.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\0bdc39bce25c90ffaf84752415c05baa\transformed\jetified-media3-common-1.4.1\AndroidManifest.xml:22:5-79
MERGED from [androidx.media3:media3-exoplayer:1.4.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\f3900c31e436e087be46264d65f1dea9\transformed\jetified-media3-exoplayer-1.4.1\AndroidManifest.xml:22:5-79
MERGED from [androidx.media3:media3-exoplayer:1.4.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\f3900c31e436e087be46264d65f1dea9\transformed\jetified-media3-exoplayer-1.4.1\AndroidManifest.xml:22:5-79
MERGED from [androidx.work:work-runtime:2.8.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\8975063235ea0ba114f3bb0240765838\transformed\work-runtime-2.8.1\AndroidManifest.xml:24:5-79
MERGED from [androidx.work:work-runtime:2.8.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\8975063235ea0ba114f3bb0240765838\transformed\work-runtime-2.8.1\AndroidManifest.xml:24:5-79
	android:name
		ADDED from [:connectivity_plus] D:\PROGRAMMING\AC-FIT\acfit_app\build\connectivity_plus\intermediates\merged_manifest\release\processReleaseManifest\AndroidManifest.xml:7:22-76
uses-permission#android.permission.WAKE_LOCK
ADDED from [androidx.work:work-runtime:2.8.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\8975063235ea0ba114f3bb0240765838\transformed\work-runtime-2.8.1\AndroidManifest.xml:23:5-68
	android:name
		ADDED from [androidx.work:work-runtime:2.8.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\8975063235ea0ba114f3bb0240765838\transformed\work-runtime-2.8.1\AndroidManifest.xml:23:22-65
uses-permission#android.permission.RECEIVE_BOOT_COMPLETED
ADDED from [androidx.work:work-runtime:2.8.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\8975063235ea0ba114f3bb0240765838\transformed\work-runtime-2.8.1\AndroidManifest.xml:25:5-81
	android:name
		ADDED from [androidx.work:work-runtime:2.8.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\8975063235ea0ba114f3bb0240765838\transformed\work-runtime-2.8.1\AndroidManifest.xml:25:22-78
uses-permission#android.permission.FOREGROUND_SERVICE
ADDED from [androidx.work:work-runtime:2.8.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\8975063235ea0ba114f3bb0240765838\transformed\work-runtime-2.8.1\AndroidManifest.xml:26:5-77
MERGED from [com.google.android.play:core:1.10.3] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\9b10a4e78f0e7750b24f9632f5e597a2\transformed\jetified-core-1.10.3\AndroidManifest.xml:13:5-77
MERGED from [com.google.android.play:core:1.10.3] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\9b10a4e78f0e7750b24f9632f5e597a2\transformed\jetified-core-1.10.3\AndroidManifest.xml:13:5-77
	android:name
		ADDED from [androidx.work:work-runtime:2.8.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\8975063235ea0ba114f3bb0240765838\transformed\work-runtime-2.8.1\AndroidManifest.xml:26:22-74
provider#androidx.startup.InitializationProvider
ADDED from [androidx.work:work-runtime:2.8.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\8975063235ea0ba114f3bb0240765838\transformed\work-runtime-2.8.1\AndroidManifest.xml:29:9-37:20
MERGED from [androidx.lifecycle:lifecycle-process:2.7.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\e920d985392df03e5b02e06877a52d21\transformed\jetified-lifecycle-process-2.7.0\AndroidManifest.xml:24:9-32:20
MERGED from [androidx.lifecycle:lifecycle-process:2.7.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\e920d985392df03e5b02e06877a52d21\transformed\jetified-lifecycle-process-2.7.0\AndroidManifest.xml:24:9-32:20
MERGED from [androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\eabc1325411a824ae74b9b450245750e\transformed\jetified-profileinstaller-1.3.1\AndroidManifest.xml:24:9-32:20
MERGED from [androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\eabc1325411a824ae74b9b450245750e\transformed\jetified-profileinstaller-1.3.1\AndroidManifest.xml:24:9-32:20
MERGED from [androidx.startup:startup-runtime:1.1.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\77d6589d54f70503af1ad7bd5c95eb07\transformed\jetified-startup-runtime-1.1.1\AndroidManifest.xml:26:9-30:34
MERGED from [androidx.startup:startup-runtime:1.1.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\77d6589d54f70503af1ad7bd5c95eb07\transformed\jetified-startup-runtime-1.1.1\AndroidManifest.xml:26:9-30:34
	tools:node
		ADDED from [androidx.work:work-runtime:2.8.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\8975063235ea0ba114f3bb0240765838\transformed\work-runtime-2.8.1\AndroidManifest.xml:33:13-31
	android:authorities
		ADDED from [androidx.work:work-runtime:2.8.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\8975063235ea0ba114f3bb0240765838\transformed\work-runtime-2.8.1\AndroidManifest.xml:31:13-68
	android:exported
		ADDED from [androidx.work:work-runtime:2.8.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\8975063235ea0ba114f3bb0240765838\transformed\work-runtime-2.8.1\AndroidManifest.xml:32:13-37
	android:name
		ADDED from [androidx.work:work-runtime:2.8.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\8975063235ea0ba114f3bb0240765838\transformed\work-runtime-2.8.1\AndroidManifest.xml:30:13-67
meta-data#androidx.work.WorkManagerInitializer
ADDED from [androidx.work:work-runtime:2.8.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\8975063235ea0ba114f3bb0240765838\transformed\work-runtime-2.8.1\AndroidManifest.xml:34:13-36:52
	android:value
		ADDED from [androidx.work:work-runtime:2.8.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\8975063235ea0ba114f3bb0240765838\transformed\work-runtime-2.8.1\AndroidManifest.xml:36:17-49
	android:name
		ADDED from [androidx.work:work-runtime:2.8.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\8975063235ea0ba114f3bb0240765838\transformed\work-runtime-2.8.1\AndroidManifest.xml:35:17-68
service#androidx.work.impl.background.systemalarm.SystemAlarmService
ADDED from [androidx.work:work-runtime:2.8.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\8975063235ea0ba114f3bb0240765838\transformed\work-runtime-2.8.1\AndroidManifest.xml:39:9-45:35
	android:enabled
		ADDED from [androidx.work:work-runtime:2.8.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\8975063235ea0ba114f3bb0240765838\transformed\work-runtime-2.8.1\AndroidManifest.xml:42:13-72
	android:exported
		ADDED from [androidx.work:work-runtime:2.8.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\8975063235ea0ba114f3bb0240765838\transformed\work-runtime-2.8.1\AndroidManifest.xml:43:13-37
	tools:ignore
		ADDED from [androidx.work:work-runtime:2.8.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\8975063235ea0ba114f3bb0240765838\transformed\work-runtime-2.8.1\AndroidManifest.xml:44:13-60
	tools:targetApi
		ADDED from [androidx.work:work-runtime:2.8.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\8975063235ea0ba114f3bb0240765838\transformed\work-runtime-2.8.1\AndroidManifest.xml:45:13-32
	android:directBootAware
		ADDED from [androidx.work:work-runtime:2.8.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\8975063235ea0ba114f3bb0240765838\transformed\work-runtime-2.8.1\AndroidManifest.xml:41:13-44
	android:name
		ADDED from [androidx.work:work-runtime:2.8.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\8975063235ea0ba114f3bb0240765838\transformed\work-runtime-2.8.1\AndroidManifest.xml:40:13-88
service#androidx.work.impl.background.systemjob.SystemJobService
ADDED from [androidx.work:work-runtime:2.8.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\8975063235ea0ba114f3bb0240765838\transformed\work-runtime-2.8.1\AndroidManifest.xml:46:9-52:35
	android:enabled
		ADDED from [androidx.work:work-runtime:2.8.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\8975063235ea0ba114f3bb0240765838\transformed\work-runtime-2.8.1\AndroidManifest.xml:49:13-70
	android:exported
		ADDED from [androidx.work:work-runtime:2.8.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\8975063235ea0ba114f3bb0240765838\transformed\work-runtime-2.8.1\AndroidManifest.xml:50:13-36
	android:permission
		ADDED from [androidx.work:work-runtime:2.8.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\8975063235ea0ba114f3bb0240765838\transformed\work-runtime-2.8.1\AndroidManifest.xml:51:13-69
	tools:targetApi
		ADDED from [androidx.work:work-runtime:2.8.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\8975063235ea0ba114f3bb0240765838\transformed\work-runtime-2.8.1\AndroidManifest.xml:52:13-32
	android:directBootAware
		ADDED from [androidx.work:work-runtime:2.8.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\8975063235ea0ba114f3bb0240765838\transformed\work-runtime-2.8.1\AndroidManifest.xml:48:13-44
	android:name
		ADDED from [androidx.work:work-runtime:2.8.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\8975063235ea0ba114f3bb0240765838\transformed\work-runtime-2.8.1\AndroidManifest.xml:47:13-84
service#androidx.work.impl.foreground.SystemForegroundService
ADDED from [androidx.work:work-runtime:2.8.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\8975063235ea0ba114f3bb0240765838\transformed\work-runtime-2.8.1\AndroidManifest.xml:53:9-59:35
	android:enabled
		ADDED from [androidx.work:work-runtime:2.8.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\8975063235ea0ba114f3bb0240765838\transformed\work-runtime-2.8.1\AndroidManifest.xml:56:13-77
	android:exported
		ADDED from [androidx.work:work-runtime:2.8.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\8975063235ea0ba114f3bb0240765838\transformed\work-runtime-2.8.1\AndroidManifest.xml:57:13-37
	tools:ignore
		ADDED from [androidx.work:work-runtime:2.8.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\8975063235ea0ba114f3bb0240765838\transformed\work-runtime-2.8.1\AndroidManifest.xml:58:13-60
	tools:targetApi
		ADDED from [androidx.work:work-runtime:2.8.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\8975063235ea0ba114f3bb0240765838\transformed\work-runtime-2.8.1\AndroidManifest.xml:59:13-32
	android:directBootAware
		ADDED from [androidx.work:work-runtime:2.8.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\8975063235ea0ba114f3bb0240765838\transformed\work-runtime-2.8.1\AndroidManifest.xml:55:13-44
	android:name
		ADDED from [androidx.work:work-runtime:2.8.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\8975063235ea0ba114f3bb0240765838\transformed\work-runtime-2.8.1\AndroidManifest.xml:54:13-81
receiver#androidx.work.impl.utils.ForceStopRunnable$BroadcastReceiver
ADDED from [androidx.work:work-runtime:2.8.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\8975063235ea0ba114f3bb0240765838\transformed\work-runtime-2.8.1\AndroidManifest.xml:61:9-66:35
	android:enabled
		ADDED from [androidx.work:work-runtime:2.8.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\8975063235ea0ba114f3bb0240765838\transformed\work-runtime-2.8.1\AndroidManifest.xml:64:13-35
	android:exported
		ADDED from [androidx.work:work-runtime:2.8.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\8975063235ea0ba114f3bb0240765838\transformed\work-runtime-2.8.1\AndroidManifest.xml:65:13-37
	tools:targetApi
		ADDED from [androidx.work:work-runtime:2.8.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\8975063235ea0ba114f3bb0240765838\transformed\work-runtime-2.8.1\AndroidManifest.xml:66:13-32
	android:directBootAware
		ADDED from [androidx.work:work-runtime:2.8.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\8975063235ea0ba114f3bb0240765838\transformed\work-runtime-2.8.1\AndroidManifest.xml:63:13-44
	android:name
		ADDED from [androidx.work:work-runtime:2.8.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\8975063235ea0ba114f3bb0240765838\transformed\work-runtime-2.8.1\AndroidManifest.xml:62:13-88
receiver#androidx.work.impl.background.systemalarm.ConstraintProxy$BatteryChargingProxy
ADDED from [androidx.work:work-runtime:2.8.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\8975063235ea0ba114f3bb0240765838\transformed\work-runtime-2.8.1\AndroidManifest.xml:67:9-77:20
	android:enabled
		ADDED from [androidx.work:work-runtime:2.8.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\8975063235ea0ba114f3bb0240765838\transformed\work-runtime-2.8.1\AndroidManifest.xml:70:13-36
	android:exported
		ADDED from [androidx.work:work-runtime:2.8.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\8975063235ea0ba114f3bb0240765838\transformed\work-runtime-2.8.1\AndroidManifest.xml:71:13-37
	tools:targetApi
		ADDED from [androidx.work:work-runtime:2.8.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\8975063235ea0ba114f3bb0240765838\transformed\work-runtime-2.8.1\AndroidManifest.xml:72:13-32
	android:directBootAware
		ADDED from [androidx.work:work-runtime:2.8.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\8975063235ea0ba114f3bb0240765838\transformed\work-runtime-2.8.1\AndroidManifest.xml:69:13-44
	android:name
		ADDED from [androidx.work:work-runtime:2.8.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\8975063235ea0ba114f3bb0240765838\transformed\work-runtime-2.8.1\AndroidManifest.xml:68:13-106
intent-filter#action:name:android.intent.action.ACTION_POWER_CONNECTED+action:name:android.intent.action.ACTION_POWER_DISCONNECTED
ADDED from [androidx.work:work-runtime:2.8.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\8975063235ea0ba114f3bb0240765838\transformed\work-runtime-2.8.1\AndroidManifest.xml:73:13-76:29
action#android.intent.action.ACTION_POWER_CONNECTED
ADDED from [androidx.work:work-runtime:2.8.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\8975063235ea0ba114f3bb0240765838\transformed\work-runtime-2.8.1\AndroidManifest.xml:74:17-87
	android:name
		ADDED from [androidx.work:work-runtime:2.8.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\8975063235ea0ba114f3bb0240765838\transformed\work-runtime-2.8.1\AndroidManifest.xml:74:25-84
action#android.intent.action.ACTION_POWER_DISCONNECTED
ADDED from [androidx.work:work-runtime:2.8.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\8975063235ea0ba114f3bb0240765838\transformed\work-runtime-2.8.1\AndroidManifest.xml:75:17-90
	android:name
		ADDED from [androidx.work:work-runtime:2.8.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\8975063235ea0ba114f3bb0240765838\transformed\work-runtime-2.8.1\AndroidManifest.xml:75:25-87
receiver#androidx.work.impl.background.systemalarm.ConstraintProxy$BatteryNotLowProxy
ADDED from [androidx.work:work-runtime:2.8.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\8975063235ea0ba114f3bb0240765838\transformed\work-runtime-2.8.1\AndroidManifest.xml:78:9-88:20
	android:enabled
		ADDED from [androidx.work:work-runtime:2.8.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\8975063235ea0ba114f3bb0240765838\transformed\work-runtime-2.8.1\AndroidManifest.xml:81:13-36
	android:exported
		ADDED from [androidx.work:work-runtime:2.8.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\8975063235ea0ba114f3bb0240765838\transformed\work-runtime-2.8.1\AndroidManifest.xml:82:13-37
	tools:targetApi
		ADDED from [androidx.work:work-runtime:2.8.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\8975063235ea0ba114f3bb0240765838\transformed\work-runtime-2.8.1\AndroidManifest.xml:83:13-32
	android:directBootAware
		ADDED from [androidx.work:work-runtime:2.8.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\8975063235ea0ba114f3bb0240765838\transformed\work-runtime-2.8.1\AndroidManifest.xml:80:13-44
	android:name
		ADDED from [androidx.work:work-runtime:2.8.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\8975063235ea0ba114f3bb0240765838\transformed\work-runtime-2.8.1\AndroidManifest.xml:79:13-104
intent-filter#action:name:android.intent.action.BATTERY_LOW+action:name:android.intent.action.BATTERY_OKAY
ADDED from [androidx.work:work-runtime:2.8.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\8975063235ea0ba114f3bb0240765838\transformed\work-runtime-2.8.1\AndroidManifest.xml:84:13-87:29
action#android.intent.action.BATTERY_OKAY
ADDED from [androidx.work:work-runtime:2.8.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\8975063235ea0ba114f3bb0240765838\transformed\work-runtime-2.8.1\AndroidManifest.xml:85:17-77
	android:name
		ADDED from [androidx.work:work-runtime:2.8.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\8975063235ea0ba114f3bb0240765838\transformed\work-runtime-2.8.1\AndroidManifest.xml:85:25-74
action#android.intent.action.BATTERY_LOW
ADDED from [androidx.work:work-runtime:2.8.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\8975063235ea0ba114f3bb0240765838\transformed\work-runtime-2.8.1\AndroidManifest.xml:86:17-76
	android:name
		ADDED from [androidx.work:work-runtime:2.8.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\8975063235ea0ba114f3bb0240765838\transformed\work-runtime-2.8.1\AndroidManifest.xml:86:25-73
receiver#androidx.work.impl.background.systemalarm.ConstraintProxy$StorageNotLowProxy
ADDED from [androidx.work:work-runtime:2.8.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\8975063235ea0ba114f3bb0240765838\transformed\work-runtime-2.8.1\AndroidManifest.xml:89:9-99:20
	android:enabled
		ADDED from [androidx.work:work-runtime:2.8.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\8975063235ea0ba114f3bb0240765838\transformed\work-runtime-2.8.1\AndroidManifest.xml:92:13-36
	android:exported
		ADDED from [androidx.work:work-runtime:2.8.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\8975063235ea0ba114f3bb0240765838\transformed\work-runtime-2.8.1\AndroidManifest.xml:93:13-37
	tools:targetApi
		ADDED from [androidx.work:work-runtime:2.8.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\8975063235ea0ba114f3bb0240765838\transformed\work-runtime-2.8.1\AndroidManifest.xml:94:13-32
	android:directBootAware
		ADDED from [androidx.work:work-runtime:2.8.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\8975063235ea0ba114f3bb0240765838\transformed\work-runtime-2.8.1\AndroidManifest.xml:91:13-44
	android:name
		ADDED from [androidx.work:work-runtime:2.8.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\8975063235ea0ba114f3bb0240765838\transformed\work-runtime-2.8.1\AndroidManifest.xml:90:13-104
intent-filter#action:name:android.intent.action.DEVICE_STORAGE_LOW+action:name:android.intent.action.DEVICE_STORAGE_OK
ADDED from [androidx.work:work-runtime:2.8.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\8975063235ea0ba114f3bb0240765838\transformed\work-runtime-2.8.1\AndroidManifest.xml:95:13-98:29
action#android.intent.action.DEVICE_STORAGE_LOW
ADDED from [androidx.work:work-runtime:2.8.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\8975063235ea0ba114f3bb0240765838\transformed\work-runtime-2.8.1\AndroidManifest.xml:96:17-83
	android:name
		ADDED from [androidx.work:work-runtime:2.8.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\8975063235ea0ba114f3bb0240765838\transformed\work-runtime-2.8.1\AndroidManifest.xml:96:25-80
action#android.intent.action.DEVICE_STORAGE_OK
ADDED from [androidx.work:work-runtime:2.8.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\8975063235ea0ba114f3bb0240765838\transformed\work-runtime-2.8.1\AndroidManifest.xml:97:17-82
	android:name
		ADDED from [androidx.work:work-runtime:2.8.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\8975063235ea0ba114f3bb0240765838\transformed\work-runtime-2.8.1\AndroidManifest.xml:97:25-79
receiver#androidx.work.impl.background.systemalarm.ConstraintProxy$NetworkStateProxy
ADDED from [androidx.work:work-runtime:2.8.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\8975063235ea0ba114f3bb0240765838\transformed\work-runtime-2.8.1\AndroidManifest.xml:100:9-109:20
	android:enabled
		ADDED from [androidx.work:work-runtime:2.8.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\8975063235ea0ba114f3bb0240765838\transformed\work-runtime-2.8.1\AndroidManifest.xml:103:13-36
	android:exported
		ADDED from [androidx.work:work-runtime:2.8.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\8975063235ea0ba114f3bb0240765838\transformed\work-runtime-2.8.1\AndroidManifest.xml:104:13-37
	tools:targetApi
		ADDED from [androidx.work:work-runtime:2.8.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\8975063235ea0ba114f3bb0240765838\transformed\work-runtime-2.8.1\AndroidManifest.xml:105:13-32
	android:directBootAware
		ADDED from [androidx.work:work-runtime:2.8.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\8975063235ea0ba114f3bb0240765838\transformed\work-runtime-2.8.1\AndroidManifest.xml:102:13-44
	android:name
		ADDED from [androidx.work:work-runtime:2.8.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\8975063235ea0ba114f3bb0240765838\transformed\work-runtime-2.8.1\AndroidManifest.xml:101:13-103
intent-filter#action:name:android.net.conn.CONNECTIVITY_CHANGE
ADDED from [androidx.work:work-runtime:2.8.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\8975063235ea0ba114f3bb0240765838\transformed\work-runtime-2.8.1\AndroidManifest.xml:106:13-108:29
action#android.net.conn.CONNECTIVITY_CHANGE
ADDED from [androidx.work:work-runtime:2.8.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\8975063235ea0ba114f3bb0240765838\transformed\work-runtime-2.8.1\AndroidManifest.xml:107:17-79
	android:name
		ADDED from [androidx.work:work-runtime:2.8.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\8975063235ea0ba114f3bb0240765838\transformed\work-runtime-2.8.1\AndroidManifest.xml:107:25-76
receiver#androidx.work.impl.background.systemalarm.RescheduleReceiver
ADDED from [androidx.work:work-runtime:2.8.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\8975063235ea0ba114f3bb0240765838\transformed\work-runtime-2.8.1\AndroidManifest.xml:110:9-121:20
	android:enabled
		ADDED from [androidx.work:work-runtime:2.8.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\8975063235ea0ba114f3bb0240765838\transformed\work-runtime-2.8.1\AndroidManifest.xml:113:13-36
	android:exported
		ADDED from [androidx.work:work-runtime:2.8.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\8975063235ea0ba114f3bb0240765838\transformed\work-runtime-2.8.1\AndroidManifest.xml:114:13-37
	tools:targetApi
		ADDED from [androidx.work:work-runtime:2.8.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\8975063235ea0ba114f3bb0240765838\transformed\work-runtime-2.8.1\AndroidManifest.xml:115:13-32
	android:directBootAware
		ADDED from [androidx.work:work-runtime:2.8.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\8975063235ea0ba114f3bb0240765838\transformed\work-runtime-2.8.1\AndroidManifest.xml:112:13-44
	android:name
		ADDED from [androidx.work:work-runtime:2.8.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\8975063235ea0ba114f3bb0240765838\transformed\work-runtime-2.8.1\AndroidManifest.xml:111:13-88
intent-filter#action:name:android.intent.action.BOOT_COMPLETED+action:name:android.intent.action.TIMEZONE_CHANGED+action:name:android.intent.action.TIME_SET
ADDED from [androidx.work:work-runtime:2.8.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\8975063235ea0ba114f3bb0240765838\transformed\work-runtime-2.8.1\AndroidManifest.xml:116:13-120:29
action#android.intent.action.BOOT_COMPLETED
ADDED from [androidx.work:work-runtime:2.8.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\8975063235ea0ba114f3bb0240765838\transformed\work-runtime-2.8.1\AndroidManifest.xml:117:17-79
	android:name
		ADDED from [androidx.work:work-runtime:2.8.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\8975063235ea0ba114f3bb0240765838\transformed\work-runtime-2.8.1\AndroidManifest.xml:117:25-76
action#android.intent.action.TIME_SET
ADDED from [androidx.work:work-runtime:2.8.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\8975063235ea0ba114f3bb0240765838\transformed\work-runtime-2.8.1\AndroidManifest.xml:118:17-73
	android:name
		ADDED from [androidx.work:work-runtime:2.8.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\8975063235ea0ba114f3bb0240765838\transformed\work-runtime-2.8.1\AndroidManifest.xml:118:25-70
action#android.intent.action.TIMEZONE_CHANGED
ADDED from [androidx.work:work-runtime:2.8.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\8975063235ea0ba114f3bb0240765838\transformed\work-runtime-2.8.1\AndroidManifest.xml:119:17-81
	android:name
		ADDED from [androidx.work:work-runtime:2.8.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\8975063235ea0ba114f3bb0240765838\transformed\work-runtime-2.8.1\AndroidManifest.xml:119:25-78
receiver#androidx.work.impl.background.systemalarm.ConstraintProxyUpdateReceiver
ADDED from [androidx.work:work-runtime:2.8.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\8975063235ea0ba114f3bb0240765838\transformed\work-runtime-2.8.1\AndroidManifest.xml:122:9-131:20
	android:enabled
		ADDED from [androidx.work:work-runtime:2.8.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\8975063235ea0ba114f3bb0240765838\transformed\work-runtime-2.8.1\AndroidManifest.xml:125:13-72
	android:exported
		ADDED from [androidx.work:work-runtime:2.8.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\8975063235ea0ba114f3bb0240765838\transformed\work-runtime-2.8.1\AndroidManifest.xml:126:13-37
	tools:targetApi
		ADDED from [androidx.work:work-runtime:2.8.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\8975063235ea0ba114f3bb0240765838\transformed\work-runtime-2.8.1\AndroidManifest.xml:127:13-32
	android:directBootAware
		ADDED from [androidx.work:work-runtime:2.8.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\8975063235ea0ba114f3bb0240765838\transformed\work-runtime-2.8.1\AndroidManifest.xml:124:13-44
	android:name
		ADDED from [androidx.work:work-runtime:2.8.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\8975063235ea0ba114f3bb0240765838\transformed\work-runtime-2.8.1\AndroidManifest.xml:123:13-99
intent-filter#action:name:androidx.work.impl.background.systemalarm.UpdateProxies
ADDED from [androidx.work:work-runtime:2.8.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\8975063235ea0ba114f3bb0240765838\transformed\work-runtime-2.8.1\AndroidManifest.xml:128:13-130:29
action#androidx.work.impl.background.systemalarm.UpdateProxies
ADDED from [androidx.work:work-runtime:2.8.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\8975063235ea0ba114f3bb0240765838\transformed\work-runtime-2.8.1\AndroidManifest.xml:129:17-98
	android:name
		ADDED from [androidx.work:work-runtime:2.8.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\8975063235ea0ba114f3bb0240765838\transformed\work-runtime-2.8.1\AndroidManifest.xml:129:25-95
receiver#androidx.work.impl.diagnostics.DiagnosticsReceiver
ADDED from [androidx.work:work-runtime:2.8.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\8975063235ea0ba114f3bb0240765838\transformed\work-runtime-2.8.1\AndroidManifest.xml:132:9-142:20
	android:enabled
		ADDED from [androidx.work:work-runtime:2.8.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\8975063235ea0ba114f3bb0240765838\transformed\work-runtime-2.8.1\AndroidManifest.xml:135:13-35
	android:exported
		ADDED from [androidx.work:work-runtime:2.8.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\8975063235ea0ba114f3bb0240765838\transformed\work-runtime-2.8.1\AndroidManifest.xml:136:13-36
	android:permission
		ADDED from [androidx.work:work-runtime:2.8.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\8975063235ea0ba114f3bb0240765838\transformed\work-runtime-2.8.1\AndroidManifest.xml:137:13-57
	tools:targetApi
		ADDED from [androidx.work:work-runtime:2.8.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\8975063235ea0ba114f3bb0240765838\transformed\work-runtime-2.8.1\AndroidManifest.xml:138:13-32
	android:directBootAware
		ADDED from [androidx.work:work-runtime:2.8.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\8975063235ea0ba114f3bb0240765838\transformed\work-runtime-2.8.1\AndroidManifest.xml:134:13-44
	android:name
		ADDED from [androidx.work:work-runtime:2.8.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\8975063235ea0ba114f3bb0240765838\transformed\work-runtime-2.8.1\AndroidManifest.xml:133:13-78
intent-filter#action:name:androidx.work.diagnostics.REQUEST_DIAGNOSTICS
ADDED from [androidx.work:work-runtime:2.8.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\8975063235ea0ba114f3bb0240765838\transformed\work-runtime-2.8.1\AndroidManifest.xml:139:13-141:29
action#androidx.work.diagnostics.REQUEST_DIAGNOSTICS
ADDED from [androidx.work:work-runtime:2.8.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\8975063235ea0ba114f3bb0240765838\transformed\work-runtime-2.8.1\AndroidManifest.xml:140:17-88
	android:name
		ADDED from [androidx.work:work-runtime:2.8.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\8975063235ea0ba114f3bb0240765838\transformed\work-runtime-2.8.1\AndroidManifest.xml:140:25-85
uses-library#androidx.window.extensions
ADDED from [androidx.window:window:1.2.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\970764a81dd610c772147726baa7dca5\transformed\jetified-window-1.2.0\AndroidManifest.xml:23:9-25:40
	android:required
		ADDED from [androidx.window:window:1.2.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\970764a81dd610c772147726baa7dca5\transformed\jetified-window-1.2.0\AndroidManifest.xml:25:13-37
	android:name
		ADDED from [androidx.window:window:1.2.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\970764a81dd610c772147726baa7dca5\transformed\jetified-window-1.2.0\AndroidManifest.xml:24:13-54
uses-library#androidx.window.sidecar
ADDED from [androidx.window:window:1.2.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\970764a81dd610c772147726baa7dca5\transformed\jetified-window-1.2.0\AndroidManifest.xml:26:9-28:40
	android:required
		ADDED from [androidx.window:window:1.2.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\970764a81dd610c772147726baa7dca5\transformed\jetified-window-1.2.0\AndroidManifest.xml:28:13-37
	android:name
		ADDED from [androidx.window:window:1.2.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\970764a81dd610c772147726baa7dca5\transformed\jetified-window-1.2.0\AndroidManifest.xml:27:13-51
meta-data#androidx.lifecycle.ProcessLifecycleInitializer
ADDED from [androidx.lifecycle:lifecycle-process:2.7.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\e920d985392df03e5b02e06877a52d21\transformed\jetified-lifecycle-process-2.7.0\AndroidManifest.xml:29:13-31:52
	android:value
		ADDED from [androidx.lifecycle:lifecycle-process:2.7.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\e920d985392df03e5b02e06877a52d21\transformed\jetified-lifecycle-process-2.7.0\AndroidManifest.xml:31:17-49
	android:name
		ADDED from [androidx.lifecycle:lifecycle-process:2.7.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\e920d985392df03e5b02e06877a52d21\transformed\jetified-lifecycle-process-2.7.0\AndroidManifest.xml:30:17-78
permission#${applicationId}.DYNAMIC_RECEIVER_NOT_EXPORTED_PERMISSION
ADDED from [androidx.core:core:1.13.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\deb838f5c4813d0c441214e1ddf497cc\transformed\core-1.13.1\AndroidManifest.xml:22:5-24:47
	android:protectionLevel
		ADDED from [androidx.core:core:1.13.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\deb838f5c4813d0c441214e1ddf497cc\transformed\core-1.13.1\AndroidManifest.xml:24:9-44
	android:name
		ADDED from [androidx.core:core:1.13.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\deb838f5c4813d0c441214e1ddf497cc\transformed\core-1.13.1\AndroidManifest.xml:23:9-81
permission#com.acfit.app.DYNAMIC_RECEIVER_NOT_EXPORTED_PERMISSION
ADDED from [androidx.core:core:1.13.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\deb838f5c4813d0c441214e1ddf497cc\transformed\core-1.13.1\AndroidManifest.xml:22:5-24:47
	android:protectionLevel
		ADDED from [androidx.core:core:1.13.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\deb838f5c4813d0c441214e1ddf497cc\transformed\core-1.13.1\AndroidManifest.xml:24:9-44
	android:name
		ADDED from [androidx.core:core:1.13.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\deb838f5c4813d0c441214e1ddf497cc\transformed\core-1.13.1\AndroidManifest.xml:23:9-81
uses-permission#${applicationId}.DYNAMIC_RECEIVER_NOT_EXPORTED_PERMISSION
ADDED from [androidx.core:core:1.13.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\deb838f5c4813d0c441214e1ddf497cc\transformed\core-1.13.1\AndroidManifest.xml:26:5-97
	android:name
		ADDED from [androidx.core:core:1.13.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\deb838f5c4813d0c441214e1ddf497cc\transformed\core-1.13.1\AndroidManifest.xml:26:22-94
uses-permission#com.acfit.app.DYNAMIC_RECEIVER_NOT_EXPORTED_PERMISSION
ADDED from [androidx.core:core:1.13.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\deb838f5c4813d0c441214e1ddf497cc\transformed\core-1.13.1\AndroidManifest.xml:26:5-97
	android:name
		ADDED from [androidx.core:core:1.13.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\deb838f5c4813d0c441214e1ddf497cc\transformed\core-1.13.1\AndroidManifest.xml:26:22-94
meta-data#androidx.profileinstaller.ProfileInstallerInitializer
ADDED from [androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\eabc1325411a824ae74b9b450245750e\transformed\jetified-profileinstaller-1.3.1\AndroidManifest.xml:29:13-31:52
	android:value
		ADDED from [androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\eabc1325411a824ae74b9b450245750e\transformed\jetified-profileinstaller-1.3.1\AndroidManifest.xml:31:17-49
	android:name
		ADDED from [androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\eabc1325411a824ae74b9b450245750e\transformed\jetified-profileinstaller-1.3.1\AndroidManifest.xml:30:17-85
receiver#androidx.profileinstaller.ProfileInstallReceiver
ADDED from [androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\eabc1325411a824ae74b9b450245750e\transformed\jetified-profileinstaller-1.3.1\AndroidManifest.xml:34:9-52:20
	android:enabled
		ADDED from [androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\eabc1325411a824ae74b9b450245750e\transformed\jetified-profileinstaller-1.3.1\AndroidManifest.xml:37:13-35
	android:exported
		ADDED from [androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\eabc1325411a824ae74b9b450245750e\transformed\jetified-profileinstaller-1.3.1\AndroidManifest.xml:38:13-36
	android:permission
		ADDED from [androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\eabc1325411a824ae74b9b450245750e\transformed\jetified-profileinstaller-1.3.1\AndroidManifest.xml:39:13-57
	android:directBootAware
		ADDED from [androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\eabc1325411a824ae74b9b450245750e\transformed\jetified-profileinstaller-1.3.1\AndroidManifest.xml:36:13-44
	android:name
		ADDED from [androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\eabc1325411a824ae74b9b450245750e\transformed\jetified-profileinstaller-1.3.1\AndroidManifest.xml:35:13-76
intent-filter#action:name:androidx.profileinstaller.action.INSTALL_PROFILE
ADDED from [androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\eabc1325411a824ae74b9b450245750e\transformed\jetified-profileinstaller-1.3.1\AndroidManifest.xml:40:13-42:29
action#androidx.profileinstaller.action.INSTALL_PROFILE
ADDED from [androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\eabc1325411a824ae74b9b450245750e\transformed\jetified-profileinstaller-1.3.1\AndroidManifest.xml:41:17-91
	android:name
		ADDED from [androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\eabc1325411a824ae74b9b450245750e\transformed\jetified-profileinstaller-1.3.1\AndroidManifest.xml:41:25-88
intent-filter#action:name:androidx.profileinstaller.action.SKIP_FILE
ADDED from [androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\eabc1325411a824ae74b9b450245750e\transformed\jetified-profileinstaller-1.3.1\AndroidManifest.xml:43:13-45:29
action#androidx.profileinstaller.action.SKIP_FILE
ADDED from [androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\eabc1325411a824ae74b9b450245750e\transformed\jetified-profileinstaller-1.3.1\AndroidManifest.xml:44:17-85
	android:name
		ADDED from [androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\eabc1325411a824ae74b9b450245750e\transformed\jetified-profileinstaller-1.3.1\AndroidManifest.xml:44:25-82
intent-filter#action:name:androidx.profileinstaller.action.SAVE_PROFILE
ADDED from [androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\eabc1325411a824ae74b9b450245750e\transformed\jetified-profileinstaller-1.3.1\AndroidManifest.xml:46:13-48:29
action#androidx.profileinstaller.action.SAVE_PROFILE
ADDED from [androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\eabc1325411a824ae74b9b450245750e\transformed\jetified-profileinstaller-1.3.1\AndroidManifest.xml:47:17-88
	android:name
		ADDED from [androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\eabc1325411a824ae74b9b450245750e\transformed\jetified-profileinstaller-1.3.1\AndroidManifest.xml:47:25-85
intent-filter#action:name:androidx.profileinstaller.action.BENCHMARK_OPERATION
ADDED from [androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\eabc1325411a824ae74b9b450245750e\transformed\jetified-profileinstaller-1.3.1\AndroidManifest.xml:49:13-51:29
action#androidx.profileinstaller.action.BENCHMARK_OPERATION
ADDED from [androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\eabc1325411a824ae74b9b450245750e\transformed\jetified-profileinstaller-1.3.1\AndroidManifest.xml:50:17-95
	android:name
		ADDED from [androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\eabc1325411a824ae74b9b450245750e\transformed\jetified-profileinstaller-1.3.1\AndroidManifest.xml:50:25-92
service#androidx.room.MultiInstanceInvalidationService
ADDED from [androidx.room:room-runtime:2.5.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\b0a143534f1d9e3ab757fbcdd209cfbb\transformed\room-runtime-2.5.0\AndroidManifest.xml:24:9-28:63
	android:exported
		ADDED from [androidx.room:room-runtime:2.5.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\b0a143534f1d9e3ab757fbcdd209cfbb\transformed\room-runtime-2.5.0\AndroidManifest.xml:27:13-37
	tools:ignore
		ADDED from [androidx.room:room-runtime:2.5.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\b0a143534f1d9e3ab757fbcdd209cfbb\transformed\room-runtime-2.5.0\AndroidManifest.xml:28:13-60
	android:directBootAware
		ADDED from [androidx.room:room-runtime:2.5.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\b0a143534f1d9e3ab757fbcdd209cfbb\transformed\room-runtime-2.5.0\AndroidManifest.xml:26:13-43
	android:name
		ADDED from [androidx.room:room-runtime:2.5.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\b0a143534f1d9e3ab757fbcdd209cfbb\transformed\room-runtime-2.5.0\AndroidManifest.xml:25:13-74
activity#com.google.android.play.core.missingsplits.PlayCoreMissingSplitsActivity
ADDED from [com.google.android.play:core:1.10.3] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\9b10a4e78f0e7750b24f9632f5e597a2\transformed\jetified-core-1.10.3\AndroidManifest.xml:18:9-24:45
	android:process
		ADDED from [com.google.android.play:core:1.10.3] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\9b10a4e78f0e7750b24f9632f5e597a2\transformed\jetified-core-1.10.3\AndroidManifest.xml:23:13-64
	android:enabled
		ADDED from [com.google.android.play:core:1.10.3] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\9b10a4e78f0e7750b24f9632f5e597a2\transformed\jetified-core-1.10.3\AndroidManifest.xml:20:13-36
	android:stateNotNeeded
		ADDED from [com.google.android.play:core:1.10.3] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\9b10a4e78f0e7750b24f9632f5e597a2\transformed\jetified-core-1.10.3\AndroidManifest.xml:24:13-42
	android:launchMode
		ADDED from [com.google.android.play:core:1.10.3] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\9b10a4e78f0e7750b24f9632f5e597a2\transformed\jetified-core-1.10.3\AndroidManifest.xml:22:13-48
	android:exported
		ADDED from [com.google.android.play:core:1.10.3] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\9b10a4e78f0e7750b24f9632f5e597a2\transformed\jetified-core-1.10.3\AndroidManifest.xml:21:13-37
	android:name
		ADDED from [com.google.android.play:core:1.10.3] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\9b10a4e78f0e7750b24f9632f5e597a2\transformed\jetified-core-1.10.3\AndroidManifest.xml:19:13-100
activity#com.google.android.play.core.common.PlayCoreDialogWrapperActivity
ADDED from [com.google.android.play:core:1.10.3] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\9b10a4e78f0e7750b24f9632f5e597a2\transformed\jetified-core-1.10.3\AndroidManifest.xml:25:9-29:65
	android:stateNotNeeded
		ADDED from [com.google.android.play:core:1.10.3] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\9b10a4e78f0e7750b24f9632f5e597a2\transformed\jetified-core-1.10.3\AndroidManifest.xml:28:13-42
	android:exported
		ADDED from [com.google.android.play:core:1.10.3] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\9b10a4e78f0e7750b24f9632f5e597a2\transformed\jetified-core-1.10.3\AndroidManifest.xml:27:13-37
	android:theme
		ADDED from [com.google.android.play:core:1.10.3] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\9b10a4e78f0e7750b24f9632f5e597a2\transformed\jetified-core-1.10.3\AndroidManifest.xml:29:13-62
	android:name
		ADDED from [com.google.android.play:core:1.10.3] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\9b10a4e78f0e7750b24f9632f5e597a2\transformed\jetified-core-1.10.3\AndroidManifest.xml:26:13-93
service#com.google.android.play.core.assetpacks.AssetPackExtractionService
ADDED from [com.google.android.play:core:1.10.3] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\9b10a4e78f0e7750b24f9632f5e597a2\transformed\jetified-core-1.10.3\AndroidManifest.xml:32:9-39:19
	android:enabled
		ADDED from [com.google.android.play:core:1.10.3] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\9b10a4e78f0e7750b24f9632f5e597a2\transformed\jetified-core-1.10.3\AndroidManifest.xml:34:13-36
	android:exported
		ADDED from [com.google.android.play:core:1.10.3] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\9b10a4e78f0e7750b24f9632f5e597a2\transformed\jetified-core-1.10.3\AndroidManifest.xml:35:13-36
	android:name
		ADDED from [com.google.android.play:core:1.10.3] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\9b10a4e78f0e7750b24f9632f5e597a2\transformed\jetified-core-1.10.3\AndroidManifest.xml:33:13-94
meta-data#com.google.android.play.core.assetpacks.versionCode
ADDED from [com.google.android.play:core:1.10.3] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\9b10a4e78f0e7750b24f9632f5e597a2\transformed\jetified-core-1.10.3\AndroidManifest.xml:36:13-38:41
	android:value
		ADDED from [com.google.android.play:core:1.10.3] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\9b10a4e78f0e7750b24f9632f5e597a2\transformed\jetified-core-1.10.3\AndroidManifest.xml:38:17-38
	android:name
		ADDED from [com.google.android.play:core:1.10.3] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\9b10a4e78f0e7750b24f9632f5e597a2\transformed\jetified-core-1.10.3\AndroidManifest.xml:37:17-83
service#com.google.android.play.core.assetpacks.ExtractionForegroundService
ADDED from [com.google.android.play:core:1.10.3] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\9b10a4e78f0e7750b24f9632f5e597a2\transformed\jetified-core-1.10.3\AndroidManifest.xml:40:9-43:40
	android:enabled
		ADDED from [com.google.android.play:core:1.10.3] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\9b10a4e78f0e7750b24f9632f5e597a2\transformed\jetified-core-1.10.3\AndroidManifest.xml:42:13-36
	android:exported
		ADDED from [com.google.android.play:core:1.10.3] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\9b10a4e78f0e7750b24f9632f5e597a2\transformed\jetified-core-1.10.3\AndroidManifest.xml:43:13-37
	android:name
		ADDED from [com.google.android.play:core:1.10.3] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\9b10a4e78f0e7750b24f9632f5e597a2\transformed\jetified-core-1.10.3\AndroidManifest.xml:41:13-95
