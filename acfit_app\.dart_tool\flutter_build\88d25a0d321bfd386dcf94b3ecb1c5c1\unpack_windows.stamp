{"inputs": ["E:\\utils\\flutter\\packages\\flutter_tools\\lib\\src\\build_system\\targets\\windows.dart", "E:\\utils\\flutter\\bin\\cache\\artifacts\\engine\\windows-x64\\flutter_windows.dll", "E:\\utils\\flutter\\bin\\cache\\artifacts\\engine\\windows-x64\\flutter_windows.dll.exp", "E:\\utils\\flutter\\bin\\cache\\artifacts\\engine\\windows-x64\\flutter_windows.dll.lib", "E:\\utils\\flutter\\bin\\cache\\artifacts\\engine\\windows-x64\\flutter_windows.dll.pdb", "E:\\utils\\flutter\\bin\\cache\\artifacts\\engine\\windows-x64\\flutter_export.h", "E:\\utils\\flutter\\bin\\cache\\artifacts\\engine\\windows-x64\\flutter_messenger.h", "E:\\utils\\flutter\\bin\\cache\\artifacts\\engine\\windows-x64\\flutter_plugin_registrar.h", "E:\\utils\\flutter\\bin\\cache\\artifacts\\engine\\windows-x64\\flutter_texture_registrar.h", "E:\\utils\\flutter\\bin\\cache\\artifacts\\engine\\windows-x64\\flutter_windows.h", "E:\\utils\\flutter\\bin\\cache\\artifacts\\engine\\windows-x64\\icudtl.dat", "E:\\utils\\flutter\\bin\\cache\\artifacts\\engine\\windows-x64\\cpp_client_wrapper\\binary_messenger_impl.h", "E:\\utils\\flutter\\bin\\cache\\artifacts\\engine\\windows-x64\\cpp_client_wrapper\\byte_buffer_streams.h", "E:\\utils\\flutter\\bin\\cache\\artifacts\\engine\\windows-x64\\cpp_client_wrapper\\core_implementations.cc", "E:\\utils\\flutter\\bin\\cache\\artifacts\\engine\\windows-x64\\cpp_client_wrapper\\engine_method_result.cc", "E:\\utils\\flutter\\bin\\cache\\artifacts\\engine\\windows-x64\\cpp_client_wrapper\\flutter_engine.cc", "E:\\utils\\flutter\\bin\\cache\\artifacts\\engine\\windows-x64\\cpp_client_wrapper\\flutter_view_controller.cc", "E:\\utils\\flutter\\bin\\cache\\artifacts\\engine\\windows-x64\\cpp_client_wrapper\\include\\flutter\\basic_message_channel.h", "E:\\utils\\flutter\\bin\\cache\\artifacts\\engine\\windows-x64\\cpp_client_wrapper\\include\\flutter\\binary_messenger.h", "E:\\utils\\flutter\\bin\\cache\\artifacts\\engine\\windows-x64\\cpp_client_wrapper\\include\\flutter\\byte_streams.h", "E:\\utils\\flutter\\bin\\cache\\artifacts\\engine\\windows-x64\\cpp_client_wrapper\\include\\flutter\\dart_project.h", "E:\\utils\\flutter\\bin\\cache\\artifacts\\engine\\windows-x64\\cpp_client_wrapper\\include\\flutter\\encodable_value.h", "E:\\utils\\flutter\\bin\\cache\\artifacts\\engine\\windows-x64\\cpp_client_wrapper\\include\\flutter\\engine_method_result.h", "E:\\utils\\flutter\\bin\\cache\\artifacts\\engine\\windows-x64\\cpp_client_wrapper\\include\\flutter\\event_channel.h", "E:\\utils\\flutter\\bin\\cache\\artifacts\\engine\\windows-x64\\cpp_client_wrapper\\include\\flutter\\event_sink.h", "E:\\utils\\flutter\\bin\\cache\\artifacts\\engine\\windows-x64\\cpp_client_wrapper\\include\\flutter\\event_stream_handler.h", "E:\\utils\\flutter\\bin\\cache\\artifacts\\engine\\windows-x64\\cpp_client_wrapper\\include\\flutter\\event_stream_handler_functions.h", "E:\\utils\\flutter\\bin\\cache\\artifacts\\engine\\windows-x64\\cpp_client_wrapper\\include\\flutter\\flutter_engine.h", "E:\\utils\\flutter\\bin\\cache\\artifacts\\engine\\windows-x64\\cpp_client_wrapper\\include\\flutter\\flutter_view.h", "E:\\utils\\flutter\\bin\\cache\\artifacts\\engine\\windows-x64\\cpp_client_wrapper\\include\\flutter\\flutter_view_controller.h", "E:\\utils\\flutter\\bin\\cache\\artifacts\\engine\\windows-x64\\cpp_client_wrapper\\include\\flutter\\message_codec.h", "E:\\utils\\flutter\\bin\\cache\\artifacts\\engine\\windows-x64\\cpp_client_wrapper\\include\\flutter\\method_call.h", "E:\\utils\\flutter\\bin\\cache\\artifacts\\engine\\windows-x64\\cpp_client_wrapper\\include\\flutter\\method_channel.h", "E:\\utils\\flutter\\bin\\cache\\artifacts\\engine\\windows-x64\\cpp_client_wrapper\\include\\flutter\\method_codec.h", "E:\\utils\\flutter\\bin\\cache\\artifacts\\engine\\windows-x64\\cpp_client_wrapper\\include\\flutter\\method_result.h", "E:\\utils\\flutter\\bin\\cache\\artifacts\\engine\\windows-x64\\cpp_client_wrapper\\include\\flutter\\method_result_functions.h", "E:\\utils\\flutter\\bin\\cache\\artifacts\\engine\\windows-x64\\cpp_client_wrapper\\include\\flutter\\plugin_registrar.h", "E:\\utils\\flutter\\bin\\cache\\artifacts\\engine\\windows-x64\\cpp_client_wrapper\\include\\flutter\\plugin_registrar_windows.h", "E:\\utils\\flutter\\bin\\cache\\artifacts\\engine\\windows-x64\\cpp_client_wrapper\\include\\flutter\\plugin_registry.h", "E:\\utils\\flutter\\bin\\cache\\artifacts\\engine\\windows-x64\\cpp_client_wrapper\\include\\flutter\\standard_codec_serializer.h", "E:\\utils\\flutter\\bin\\cache\\artifacts\\engine\\windows-x64\\cpp_client_wrapper\\include\\flutter\\standard_message_codec.h", "E:\\utils\\flutter\\bin\\cache\\artifacts\\engine\\windows-x64\\cpp_client_wrapper\\include\\flutter\\standard_method_codec.h", "E:\\utils\\flutter\\bin\\cache\\artifacts\\engine\\windows-x64\\cpp_client_wrapper\\include\\flutter\\texture_registrar.h", "E:\\utils\\flutter\\bin\\cache\\artifacts\\engine\\windows-x64\\cpp_client_wrapper\\plugin_registrar.cc", "E:\\utils\\flutter\\bin\\cache\\artifacts\\engine\\windows-x64\\cpp_client_wrapper\\readme", "E:\\utils\\flutter\\bin\\cache\\artifacts\\engine\\windows-x64\\cpp_client_wrapper\\standard_codec.cc", "E:\\utils\\flutter\\bin\\cache\\artifacts\\engine\\windows-x64\\cpp_client_wrapper\\texture_registrar_impl.h"], "outputs": ["D:\\PROGRAMMING\\AC-FIT\\acfit_app\\windows\\flutter\\ephemeral\\flutter_windows.dll", "D:\\PROGRAMMING\\AC-FIT\\acfit_app\\windows\\flutter\\ephemeral\\flutter_windows.dll.exp", "D:\\PROGRAMMING\\AC-FIT\\acfit_app\\windows\\flutter\\ephemeral\\flutter_windows.dll.lib", "D:\\PROGRAMMING\\AC-FIT\\acfit_app\\windows\\flutter\\ephemeral\\flutter_windows.dll.pdb", "D:\\PROGRAMMING\\AC-FIT\\acfit_app\\windows\\flutter\\ephemeral\\flutter_export.h", "D:\\PROGRAMMING\\AC-FIT\\acfit_app\\windows\\flutter\\ephemeral\\flutter_messenger.h", "D:\\PROGRAMMING\\AC-FIT\\acfit_app\\windows\\flutter\\ephemeral\\flutter_plugin_registrar.h", "D:\\PROGRAMMING\\AC-FIT\\acfit_app\\windows\\flutter\\ephemeral\\flutter_texture_registrar.h", "D:\\PROGRAMMING\\AC-FIT\\acfit_app\\windows\\flutter\\ephemeral\\flutter_windows.h", "D:\\PROGRAMMING\\AC-FIT\\acfit_app\\windows\\flutter\\ephemeral\\icudtl.dat", "D:\\PROGRAMMING\\AC-FIT\\acfit_app\\windows\\flutter\\ephemeral\\cpp_client_wrapper\\binary_messenger_impl.h", "D:\\PROGRAMMING\\AC-FIT\\acfit_app\\windows\\flutter\\ephemeral\\cpp_client_wrapper\\byte_buffer_streams.h", "D:\\PROGRAMMING\\AC-FIT\\acfit_app\\windows\\flutter\\ephemeral\\cpp_client_wrapper\\core_implementations.cc", "D:\\PROGRAMMING\\AC-FIT\\acfit_app\\windows\\flutter\\ephemeral\\cpp_client_wrapper\\engine_method_result.cc", "D:\\PROGRAMMING\\AC-FIT\\acfit_app\\windows\\flutter\\ephemeral\\cpp_client_wrapper\\flutter_engine.cc", "D:\\PROGRAMMING\\AC-FIT\\acfit_app\\windows\\flutter\\ephemeral\\cpp_client_wrapper\\flutter_view_controller.cc", "D:\\PROGRAMMING\\AC-FIT\\acfit_app\\windows\\flutter\\ephemeral\\cpp_client_wrapper\\include\\flutter\\basic_message_channel.h", "D:\\PROGRAMMING\\AC-FIT\\acfit_app\\windows\\flutter\\ephemeral\\cpp_client_wrapper\\include\\flutter\\binary_messenger.h", "D:\\PROGRAMMING\\AC-FIT\\acfit_app\\windows\\flutter\\ephemeral\\cpp_client_wrapper\\include\\flutter\\byte_streams.h", "D:\\PROGRAMMING\\AC-FIT\\acfit_app\\windows\\flutter\\ephemeral\\cpp_client_wrapper\\include\\flutter\\dart_project.h", "D:\\PROGRAMMING\\AC-FIT\\acfit_app\\windows\\flutter\\ephemeral\\cpp_client_wrapper\\include\\flutter\\encodable_value.h", "D:\\PROGRAMMING\\AC-FIT\\acfit_app\\windows\\flutter\\ephemeral\\cpp_client_wrapper\\include\\flutter\\engine_method_result.h", "D:\\PROGRAMMING\\AC-FIT\\acfit_app\\windows\\flutter\\ephemeral\\cpp_client_wrapper\\include\\flutter\\event_channel.h", "D:\\PROGRAMMING\\AC-FIT\\acfit_app\\windows\\flutter\\ephemeral\\cpp_client_wrapper\\include\\flutter\\event_sink.h", "D:\\PROGRAMMING\\AC-FIT\\acfit_app\\windows\\flutter\\ephemeral\\cpp_client_wrapper\\include\\flutter\\event_stream_handler.h", "D:\\PROGRAMMING\\AC-FIT\\acfit_app\\windows\\flutter\\ephemeral\\cpp_client_wrapper\\include\\flutter\\event_stream_handler_functions.h", "D:\\PROGRAMMING\\AC-FIT\\acfit_app\\windows\\flutter\\ephemeral\\cpp_client_wrapper\\include\\flutter\\flutter_engine.h", "D:\\PROGRAMMING\\AC-FIT\\acfit_app\\windows\\flutter\\ephemeral\\cpp_client_wrapper\\include\\flutter\\flutter_view.h", "D:\\PROGRAMMING\\AC-FIT\\acfit_app\\windows\\flutter\\ephemeral\\cpp_client_wrapper\\include\\flutter\\flutter_view_controller.h", "D:\\PROGRAMMING\\AC-FIT\\acfit_app\\windows\\flutter\\ephemeral\\cpp_client_wrapper\\include\\flutter\\message_codec.h", "D:\\PROGRAMMING\\AC-FIT\\acfit_app\\windows\\flutter\\ephemeral\\cpp_client_wrapper\\include\\flutter\\method_call.h", "D:\\PROGRAMMING\\AC-FIT\\acfit_app\\windows\\flutter\\ephemeral\\cpp_client_wrapper\\include\\flutter\\method_channel.h", "D:\\PROGRAMMING\\AC-FIT\\acfit_app\\windows\\flutter\\ephemeral\\cpp_client_wrapper\\include\\flutter\\method_codec.h", "D:\\PROGRAMMING\\AC-FIT\\acfit_app\\windows\\flutter\\ephemeral\\cpp_client_wrapper\\include\\flutter\\method_result.h", "D:\\PROGRAMMING\\AC-FIT\\acfit_app\\windows\\flutter\\ephemeral\\cpp_client_wrapper\\include\\flutter\\method_result_functions.h", "D:\\PROGRAMMING\\AC-FIT\\acfit_app\\windows\\flutter\\ephemeral\\cpp_client_wrapper\\include\\flutter\\plugin_registrar.h", "D:\\PROGRAMMING\\AC-FIT\\acfit_app\\windows\\flutter\\ephemeral\\cpp_client_wrapper\\include\\flutter\\plugin_registrar_windows.h", "D:\\PROGRAMMING\\AC-FIT\\acfit_app\\windows\\flutter\\ephemeral\\cpp_client_wrapper\\include\\flutter\\plugin_registry.h", "D:\\PROGRAMMING\\AC-FIT\\acfit_app\\windows\\flutter\\ephemeral\\cpp_client_wrapper\\include\\flutter\\standard_codec_serializer.h", "D:\\PROGRAMMING\\AC-FIT\\acfit_app\\windows\\flutter\\ephemeral\\cpp_client_wrapper\\include\\flutter\\standard_message_codec.h", "D:\\PROGRAMMING\\AC-FIT\\acfit_app\\windows\\flutter\\ephemeral\\cpp_client_wrapper\\include\\flutter\\standard_method_codec.h", "D:\\PROGRAMMING\\AC-FIT\\acfit_app\\windows\\flutter\\ephemeral\\cpp_client_wrapper\\include\\flutter\\texture_registrar.h", "D:\\PROGRAMMING\\AC-FIT\\acfit_app\\windows\\flutter\\ephemeral\\cpp_client_wrapper\\plugin_registrar.cc", "D:\\PROGRAMMING\\AC-FIT\\acfit_app\\windows\\flutter\\ephemeral\\cpp_client_wrapper\\readme", "D:\\PROGRAMMING\\AC-FIT\\acfit_app\\windows\\flutter\\ephemeral\\cpp_client_wrapper\\standard_codec.cc", "D:\\PROGRAMMING\\AC-FIT\\acfit_app\\windows\\flutter\\ephemeral\\cpp_client_wrapper\\texture_registrar_impl.h"]}