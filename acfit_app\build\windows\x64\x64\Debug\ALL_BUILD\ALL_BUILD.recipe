﻿<?xml version="1.0" encoding="utf-8"?>
<Project>
  <ProjectOutputs>
    <ProjectOutput>
      <FullPath>D:\PROGRAMMING\AC-FIT\acfit_app\build\windows\x64\x64\Debug\ZERO_CHECK</FullPath>
    </ProjectOutput>
    <ProjectOutput>
      <FullPath>D:\PROGRAMMING\AC-FIT\acfit_app\build\windows\x64\flutter\x64\Debug\flutter_assemble</FullPath>
    </ProjectOutput>
    <ProjectOutput>
      <FullPath>D:\PROGRAMMING\AC-FIT\acfit_app\build\windows\x64\plugins\audioplayers_windows\Debug\audioplayers_windows_plugin.dll</FullPath>
    </ProjectOutput>
    <ProjectOutput>
      <FullPath>D:\PROGRAMMING\AC-FIT\acfit_app\build\windows\x64\plugins\connectivity_plus\Debug\connectivity_plus_plugin.dll</FullPath>
    </ProjectOutput>
    <ProjectOutput>
      <FullPath>D:\PROGRAMMING\AC-FIT\acfit_app\build\windows\x64\plugins\file_selector_windows\Debug\file_selector_windows_plugin.dll</FullPath>
    </ProjectOutput>
    <ProjectOutput>
      <FullPath>D:\PROGRAMMING\AC-FIT\acfit_app\build\windows\x64\plugins\flutter_secure_storage_windows\Debug\flutter_secure_storage_windows_plugin.dll</FullPath>
    </ProjectOutput>
    <ProjectOutput>
      <FullPath>D:\PROGRAMMING\AC-FIT\acfit_app\build\windows\x64\plugins\flutter_timezone\Debug\flutter_timezone_plugin.dll</FullPath>
    </ProjectOutput>
    <ProjectOutput>
      <FullPath>D:\PROGRAMMING\AC-FIT\acfit_app\build\windows\x64\plugins\url_launcher_windows\Debug\url_launcher_windows_plugin.dll</FullPath>
    </ProjectOutput>
    <ProjectOutput>
      <FullPath>D:\PROGRAMMING\AC-FIT\acfit_app\build\windows\x64\runner\Debug\acfit_app.exe</FullPath>
    </ProjectOutput>
    <ProjectOutput>
      <FullPath>D:\PROGRAMMING\AC-FIT\acfit_app\build\windows\x64\plugins\flutter_local_notifications_windows\shared\Debug\flutter_local_notifications_windows.dll</FullPath>
    </ProjectOutput>
    <ProjectOutput>
      <FullPath>D:\PROGRAMMING\AC-FIT\acfit_app\build\windows\x64\x64\Debug\ALL_BUILD</FullPath>
    </ProjectOutput>
  </ProjectOutputs>
  <ContentFiles />
  <SatelliteDlls />
  <NonRecipeFileRefs />
</Project>