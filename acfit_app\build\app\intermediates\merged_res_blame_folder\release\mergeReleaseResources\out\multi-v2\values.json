{"logs": [{"outputFile": "com.example.acfit_app-mergeReleaseResources-49:/values/values.xml", "map": [{"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.10.2\\transforms\\32d7d149835c8c00f0697dfcab9adb84\\transformed\\appcompat-1.1.0\\res\\values\\values.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,54,55,56,57,58,60,61,62,63,64,65,66,67,68,69,70,71,72,73,74,75,76,77,78,79,80,81,82,83,84,85,86,87,88,89,90,91,92,93,94,95,96,97,98,99,100,101,102,103,104,105,106,107,108,109,110,111,112,113,114,115,116,117,118,119,120,121,122,123,124,125,126,127,128,129,130,131,132,133,134,135,136,137,138,139,140,141,142,143,144,145,146,147,148,149,150,151,152,153,154,155,156,157,158,159,160,161,162,163,164,165,166,167,168,169,170,171,172,173,174,176,177,178,179,180,181,182,183,184,185,186,187,188,189,190,191,192,193,194,195,196,197,198,199,200,201,202,203,204,205,206,207,208,209,210,218,219,223,227,231,236,242,249,253,257,262,266,270,274,278,282,286,292,296,302,306,312,316,321,325,328,332,338,342,348,352,358,361,365,369,373,377,381,382,383,384,387,390,393,396,400,401,402,403,404,407,409,411,413,418,419,423,429,433,434,436,447,448,452,458,462,463,464,468,495,499,500,504,532,703,729,901,927,958,966,972,986,1008,1013,1018,1028,1037,1046,1050,1057,1065,1072,1073,1082,1085,1088,1092,1096,1100,1103,1104,1109,1114,1124,1129,1136,1142,1143,1146,1150,1155,1157,1159,1162,1165,1167,1171,1174,1181,1184,1187,1191,1193,1197,1199,1201,1203,1207,1215,1223,1235,1241,1250,1253,1264,1267,1268,1273,1274,1279,1348,1418,1419,1429,1438,1439,1441,1445,1448,1451,1454,1457,1460,1463,1466,1470,1473,1476,1479,1483,1486,1490,1494,1495,1496,1497,1498,1499,1500,1501,1502,1503,1504,1505,1506,1507,1508,1509,1510,1511,1512,1513,1514,1516,1518,1519,1520,1521,1522,1523,1524,1525,1527,1528,1530,1531,1533,1535,1536,1538,1539,1540,1541,1542,1543,1545,1546,1547,1548,1549,1550,1552,1554,1556,1557,1558,1559,1560,1561,1562,1563,1564,1565,1566,1567,1568,1570,1571,1572,1573,1574,1575,1577,1581,1585,1586,1587,1588,1589,1590,1594,1595,1596,1597,1599,1601,1603,1605,1607,1608,1609,1610,1612,1614,1616,1617,1618,1619,1620,1621,1622,1623,1624,1625,1626,1627,1630,1631,1632,1633,1635,1637,1638,1640,1641,1643,1645,1647,1648,1649,1650,1651,1652,1653,1654,1655,1656,1657,1658,1660,1661,1662,1663,1665,1666,1667,1668,1669,1671,1673,1675,1677,1678,1679,1680,1681,1682,1683,1684,1685,1686,1687,1688,1689,1690,1691,1692,1775,1778,1781,1784,1798,1809,1819,1849,1876,1885,1960,2357,2362,2390,2408,2444,2450,2456,2479,2620,2640,2646,2650,2656,2693,2705,2771,2795,2864,2883,2909", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "105,160,205,254,295,350,409,471,552,613,688,764,841,919,1004,1086,1162,1238,1315,1393,1499,1605,1684,1764,1821,1879,1953,2028,2093,2159,2219,2280,2352,2425,2492,2560,2619,2678,2737,2796,2855,2909,2963,3016,3070,3124,3178,3232,3306,3385,3458,3603,3675,3747,3820,3877,4008,4082,4156,4231,4303,4376,4446,4517,4577,4638,4707,4776,4846,4920,4996,5060,5137,5213,5290,5355,5424,5501,5576,5645,5713,5790,5856,5917,6014,6079,6148,6247,6318,6377,6435,6492,6551,6615,6686,6758,6830,6902,6974,7041,7109,7177,7236,7299,7363,7453,7544,7604,7670,7737,7803,7873,7937,7990,8057,8118,8185,8298,8356,8419,8484,8549,8624,8697,8769,8818,8879,8940,9001,9063,9127,9191,9255,9320,9383,9443,9504,9570,9629,9689,9751,9822,9882,9950,10036,10123,10213,10300,10388,10470,10553,10643,10734,10786,10844,10889,10955,11019,11076,11133,11187,11244,11292,11341,11392,11426,11473,11522,11568,11600,11664,11786,11843,11917,11987,12065,12119,12189,12274,12322,12368,12429,12492,12558,12622,12693,12756,12821,12885,12946,13007,13059,13132,13206,13275,13350,13424,13498,13639,13709,13762,13840,13930,14018,14114,14204,14786,14875,15122,15403,15655,15940,16333,16810,17032,17254,17530,17757,17987,18217,18447,18677,18904,19323,19549,19974,20204,20632,20851,21134,21342,21473,21700,22126,22351,22778,22999,23424,23544,23820,24121,24445,24736,25050,25187,25318,25423,25665,25832,26036,26244,26515,26627,26739,26844,26961,27175,27321,27461,27547,27895,27983,28229,28647,28896,28978,29076,29693,29793,30045,30469,30724,30818,30907,31144,33196,33438,33540,33793,35977,47101,48617,59840,61368,63125,63751,64171,65232,66497,66753,66989,67536,68030,68635,68833,69413,69977,70352,70470,71008,71165,71361,71634,71890,72060,72201,72265,72630,72997,73673,73937,74275,74628,74722,74908,75214,75476,75601,75728,75967,76178,76297,76490,76667,77122,77303,77425,77684,77797,77984,78086,78193,78322,78597,79105,79601,80478,80772,81342,81491,82223,82395,82479,82815,82907,83185,88594,94146,94208,94838,95452,95543,95656,95885,96045,96197,96368,96534,96703,96870,97033,97276,97446,97619,97790,98064,98263,98468,98798,98882,98978,99074,99172,99272,99374,99476,99578,99680,99782,99882,99978,100090,100219,100342,100473,100604,100702,100816,100910,101050,101184,101280,101392,101492,101608,101704,101816,101916,102056,102192,102356,102486,102644,102794,102935,103079,103214,103326,103476,103604,103732,103868,104000,104130,104260,104372,104512,104658,104802,104940,105006,105096,105172,105276,105366,105468,105576,105684,105784,105864,105956,106054,106164,106242,106348,106440,106544,106654,106776,106939,107096,107176,107276,107366,107476,107566,107807,107901,108007,108099,108199,108311,108425,108541,108657,108751,108865,108977,109079,109199,109321,109403,109507,109627,109753,109851,109945,110033,110145,110261,110383,110495,110670,110786,110872,110964,111076,111200,111267,111393,111461,111589,111733,111861,111930,112025,112140,112253,112352,112461,112572,112683,112784,112889,112989,113119,113210,113333,113427,113539,113625,113729,113825,113913,114031,114135,114239,114365,114453,114561,114661,114751,114861,114945,115047,115131,115185,115249,115355,115441,115551,115635,115755,120899,121017,121132,121264,121979,122671,123188,124787,126320,126708,131443,151705,151965,153475,154508,156521,156783,157139,157969,164751,165885,166179,166402,166729,168779,169427,173278,174480,178559,179774,181183", "endLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,54,55,56,57,58,60,61,62,63,64,65,66,67,68,69,70,71,72,73,74,75,76,77,78,79,80,81,82,83,84,85,86,87,88,89,90,91,92,93,94,95,96,97,98,99,100,101,102,103,104,105,106,107,108,109,110,111,112,113,114,115,116,117,118,119,120,121,122,123,124,125,126,127,128,129,130,131,132,133,134,135,136,137,138,139,140,141,142,143,144,145,146,147,148,149,150,151,152,153,154,155,156,157,158,159,160,161,162,163,164,165,166,167,168,169,170,171,172,173,174,176,177,178,179,180,181,182,183,184,185,186,187,188,189,190,191,192,193,194,195,196,197,198,199,200,201,202,203,204,205,206,207,208,209,217,218,222,226,230,235,241,248,252,256,261,265,269,273,277,281,285,291,295,301,305,311,315,320,324,327,331,337,341,347,351,357,360,364,368,372,376,380,381,382,383,386,389,392,395,399,400,401,402,403,406,408,410,412,417,418,422,428,432,433,435,446,447,451,457,461,462,463,467,494,498,499,503,531,702,728,900,926,957,965,971,985,1007,1012,1017,1027,1036,1045,1049,1056,1064,1071,1072,1081,1084,1087,1091,1095,1099,1102,1103,1108,1113,1123,1128,1135,1141,1142,1145,1149,1154,1156,1158,1161,1164,1166,1170,1173,1180,1183,1186,1190,1192,1196,1198,1200,1202,1206,1214,1222,1234,1240,1249,1252,1263,1266,1267,1272,1273,1278,1347,1417,1418,1428,1437,1438,1440,1444,1447,1450,1453,1456,1459,1462,1465,1469,1472,1475,1478,1482,1485,1489,1493,1494,1495,1496,1497,1498,1499,1500,1501,1502,1503,1504,1505,1506,1507,1508,1509,1510,1511,1512,1513,1515,1517,1518,1519,1520,1521,1522,1523,1524,1526,1527,1529,1530,1532,1534,1535,1537,1538,1539,1540,1541,1542,1544,1545,1546,1547,1548,1549,1551,1553,1555,1556,1557,1558,1559,1560,1561,1562,1563,1564,1565,1566,1567,1569,1570,1571,1572,1573,1574,1576,1580,1584,1585,1586,1587,1588,1589,1593,1594,1595,1596,1598,1600,1602,1604,1606,1607,1608,1609,1611,1613,1615,1616,1617,1618,1619,1620,1621,1622,1623,1624,1625,1626,1629,1630,1631,1632,1634,1636,1637,1639,1640,1642,1644,1646,1647,1648,1649,1650,1651,1652,1653,1654,1655,1656,1657,1659,1660,1661,1662,1664,1665,1666,1667,1668,1670,1672,1674,1676,1677,1678,1679,1680,1681,1682,1683,1684,1685,1686,1687,1688,1689,1690,1691,1774,1777,1780,1783,1797,1808,1818,1848,1875,1884,1959,2356,2361,2389,2407,2443,2449,2455,2478,2619,2639,2645,2649,2655,2692,2704,2770,2794,2863,2882,2908,2917", "endColumns": "54,44,48,40,54,58,61,80,60,74,75,76,77,84,81,75,75,76,77,105,105,78,79,56,57,73,74,64,65,59,60,71,72,66,67,58,58,58,58,58,53,53,52,53,53,53,53,73,78,72,73,71,71,72,56,57,73,73,74,71,72,69,70,59,60,68,68,69,73,75,63,76,75,76,64,68,76,74,68,67,76,65,60,96,64,68,98,70,58,57,56,58,63,70,71,71,71,71,66,67,67,58,62,63,89,90,59,65,66,65,69,63,52,66,60,66,112,57,62,64,64,74,72,71,48,60,60,60,61,63,63,63,64,62,59,60,65,58,59,61,70,59,67,85,86,89,86,87,81,82,89,90,51,57,44,65,63,56,56,53,56,47,48,50,33,46,48,45,31,63,61,56,73,69,77,53,69,84,47,45,60,62,65,63,70,62,64,63,60,60,51,72,73,68,74,73,73,140,69,52,77,89,87,95,89,12,88,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,136,130,104,12,12,12,12,12,111,111,104,116,12,12,12,12,12,87,12,12,12,81,12,12,99,12,12,12,93,88,12,12,12,101,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,117,12,12,12,12,12,12,12,63,12,12,12,12,12,12,93,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,83,12,91,12,12,12,61,12,12,90,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,83,95,95,97,99,101,101,101,101,101,99,95,111,128,122,130,130,97,113,93,12,12,95,111,99,115,95,111,99,12,135,12,129,12,12,140,12,134,111,149,127,127,12,131,129,129,111,139,12,12,12,65,89,75,103,89,101,107,107,99,79,91,97,12,77,105,91,103,109,12,12,12,79,99,89,109,89,12,93,105,91,12,12,12,12,12,93,113,111,12,12,12,81,103,119,125,97,93,87,111,115,121,111,12,115,85,91,12,12,66,12,67,12,12,12,68,94,114,112,98,108,110,110,100,104,99,12,90,122,93,12,85,103,95,87,12,12,12,12,87,107,99,89,109,83,101,83,53,63,105,85,109,83,119,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24", "endOffsets": "155,200,249,290,345,404,466,547,608,683,759,836,914,999,1081,1157,1233,1310,1388,1494,1600,1679,1759,1816,1874,1948,2023,2088,2154,2214,2275,2347,2420,2487,2555,2614,2673,2732,2791,2850,2904,2958,3011,3065,3119,3173,3227,3301,3380,3453,3527,3670,3742,3815,3872,3930,4077,4151,4226,4298,4371,4441,4512,4572,4633,4702,4771,4841,4915,4991,5055,5132,5208,5285,5350,5419,5496,5571,5640,5708,5785,5851,5912,6009,6074,6143,6242,6313,6372,6430,6487,6546,6610,6681,6753,6825,6897,6969,7036,7104,7172,7231,7294,7358,7448,7539,7599,7665,7732,7798,7868,7932,7985,8052,8113,8180,8293,8351,8414,8479,8544,8619,8692,8764,8813,8874,8935,8996,9058,9122,9186,9250,9315,9378,9438,9499,9565,9624,9684,9746,9817,9877,9945,10031,10118,10208,10295,10383,10465,10548,10638,10729,10781,10839,10884,10950,11014,11071,11128,11182,11239,11287,11336,11387,11421,11468,11517,11563,11595,11659,11721,11838,11912,11982,12060,12114,12184,12269,12317,12363,12424,12487,12553,12617,12688,12751,12816,12880,12941,13002,13054,13127,13201,13270,13345,13419,13493,13634,13704,13757,13835,13925,14013,14109,14199,14781,14870,15117,15398,15650,15935,16328,16805,17027,17249,17525,17752,17982,18212,18442,18672,18899,19318,19544,19969,20199,20627,20846,21129,21337,21468,21695,22121,22346,22773,22994,23419,23539,23815,24116,24440,24731,25045,25182,25313,25418,25660,25827,26031,26239,26510,26622,26734,26839,26956,27170,27316,27456,27542,27890,27978,28224,28642,28891,28973,29071,29688,29788,30040,30464,30719,30813,30902,31139,33191,33433,33535,33788,35972,47096,48612,59835,61363,63120,63746,64166,65227,66492,66748,66984,67531,68025,68630,68828,69408,69972,70347,70465,71003,71160,71356,71629,71885,72055,72196,72260,72625,72992,73668,73932,74270,74623,74717,74903,75209,75471,75596,75723,75962,76173,76292,76485,76662,77117,77298,77420,77679,77792,77979,78081,78188,78317,78592,79100,79596,80473,80767,81337,81486,82218,82390,82474,82810,82902,83180,88589,94141,94203,94833,95447,95538,95651,95880,96040,96192,96363,96529,96698,96865,97028,97271,97441,97614,97785,98059,98258,98463,98793,98877,98973,99069,99167,99267,99369,99471,99573,99675,99777,99877,99973,100085,100214,100337,100468,100599,100697,100811,100905,101045,101179,101275,101387,101487,101603,101699,101811,101911,102051,102187,102351,102481,102639,102789,102930,103074,103209,103321,103471,103599,103727,103863,103995,104125,104255,104367,104507,104653,104797,104935,105001,105091,105167,105271,105361,105463,105571,105679,105779,105859,105951,106049,106159,106237,106343,106435,106539,106649,106771,106934,107091,107171,107271,107361,107471,107561,107802,107896,108002,108094,108194,108306,108420,108536,108652,108746,108860,108972,109074,109194,109316,109398,109502,109622,109748,109846,109940,110028,110140,110256,110378,110490,110665,110781,110867,110959,111071,111195,111262,111388,111456,111584,111728,111856,111925,112020,112135,112248,112347,112456,112567,112678,112779,112884,112984,113114,113205,113328,113422,113534,113620,113724,113820,113908,114026,114130,114234,114360,114448,114556,114656,114746,114856,114940,115042,115126,115180,115244,115350,115436,115546,115630,115750,120894,121012,121127,121259,121974,122666,123183,124782,126315,126703,131438,151700,151960,153470,154503,156516,156778,157134,157964,164746,165880,166174,166397,166724,168774,169422,173273,174475,178554,179769,181178,181652"}, "to": {"startLines": "4,27,28,59,60,61,62,68,69,70,71,72,73,76,77,78,79,80,81,82,83,84,85,90,91,94,95,96,97,98,99,100,101,102,103,104,105,106,107,108,109,110,111,112,113,114,115,120,121,122,123,125,126,127,128,129,131,132,133,134,135,136,137,138,139,140,141,142,143,144,145,146,147,148,149,150,151,152,153,154,155,156,157,158,159,160,161,162,163,164,165,166,167,168,169,170,171,172,173,174,175,176,177,178,179,180,181,182,183,184,185,186,187,188,189,190,191,192,193,194,195,196,197,198,199,200,201,202,203,204,205,206,207,208,209,210,211,212,213,214,215,216,217,227,228,232,233,234,235,236,237,238,268,269,270,271,272,273,274,275,311,312,313,314,319,327,328,333,355,361,362,364,368,369,370,371,372,373,374,375,376,377,378,379,380,381,382,383,384,385,386,387,388,389,390,391,392,393,394,422,427,428,429,430,431,432,440,441,445,449,453,458,464,471,475,479,484,488,492,496,500,504,508,514,518,524,528,534,538,543,547,550,554,560,564,570,574,580,583,587,591,595,599,603,604,605,606,609,612,615,618,622,623,624,625,626,629,631,633,635,640,641,645,651,655,656,658,669,670,674,680,684,685,686,690,717,721,722,726,754,924,950,1121,1147,1178,1186,1192,1206,1228,1233,1238,1248,1257,1266,1270,1277,1285,1292,1293,1302,1305,1308,1312,1316,1320,1323,1324,1329,1334,1344,1349,1356,1362,1363,1366,1370,1375,1377,1379,1382,1385,1387,1391,1394,1401,1404,1407,1411,1413,1417,1419,1421,1423,1427,1435,1443,1455,1461,1470,1473,1484,1487,1488,1493,1494,1527,1596,1666,1667,1677,1686,1838,1840,1844,1847,1850,1853,1856,1859,1862,1865,1869,1872,1875,1878,1882,1885,1889,1893,1894,1895,1896,1897,1898,1899,1900,1901,1902,1903,1904,1905,1906,1907,1908,1909,1910,1911,1912,1913,1915,1917,1918,1919,1920,1921,1922,1923,1924,1926,1927,1929,1930,1932,1934,1935,1937,1938,1939,1940,1941,1942,1944,1945,1946,1947,1948,1965,1967,1969,1971,1972,1973,1974,1975,1976,1977,1978,1979,1980,1981,1982,1983,1985,1986,1987,1988,1989,1990,1992,1996,2008,2009,2010,2011,2012,2013,2017,2018,2019,2020,2022,2024,2026,2028,2030,2031,2032,2033,2035,2037,2039,2040,2041,2042,2043,2044,2045,2046,2047,2048,2049,2050,2053,2054,2055,2056,2058,2060,2061,2063,2064,2066,2068,2070,2071,2072,2073,2074,2075,2076,2077,2078,2079,2080,2081,2083,2084,2085,2086,2088,2089,2090,2091,2092,2094,2096,2098,2100,2101,2102,2103,2104,2105,2106,2107,2108,2109,2110,2111,2112,2113,2114,2120,2195,2198,2201,2204,2218,2235,2277,2306,2333,2342,2404,2768,2799,2937,3061,3085,3091,3107,3128,3252,3280,3286,3430,3456,3504,3575,3675,3695,3750,3762,3788", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "225,891,936,1983,2024,2079,2138,2533,2614,2675,2750,2826,2903,3141,3226,3308,3384,3460,3537,3615,3721,3827,3906,4235,4292,4481,4555,4630,4695,4761,4821,4882,4954,5027,5094,5162,5221,5280,5339,5398,5457,5511,5565,5618,5672,5726,5780,6124,6198,6277,6350,6495,6567,6639,6712,6769,6900,6974,7048,7123,7195,7268,7338,7409,7469,7530,7599,7668,7738,7812,7888,7952,8029,8105,8182,8247,8316,8393,8468,8537,8605,8682,8748,8809,8906,8971,9040,9139,9210,9269,9327,9384,9443,9507,9578,9650,9722,9794,9866,9933,10001,10069,10128,10191,10255,10345,10436,10496,10562,10629,10695,10765,10829,10882,10949,11010,11077,11190,11248,11311,11376,11441,11516,11589,11661,11710,11771,11832,11893,11955,12019,12083,12147,12212,12275,12335,12396,12462,12521,12581,12643,12714,12774,13473,13559,13809,13899,13986,14074,14156,14239,14329,16266,16318,16376,16421,16487,16551,16608,16665,18842,18899,18947,18996,19251,19621,19668,19926,21097,21400,21464,21586,21839,21913,21983,22061,22115,22185,22270,22318,22364,22425,22488,22554,22618,22689,22752,22817,22881,22942,23003,23055,23128,23202,23271,23346,23420,23494,23635,25507,25868,25946,26036,26124,26220,26310,26892,26981,27228,27509,27761,28046,28439,28916,29138,29360,29636,29863,30093,30323,30553,30783,31010,31429,31655,32080,32310,32738,32957,33240,33448,33579,33806,34232,34457,34884,35105,35530,35650,35926,36227,36551,36842,37156,37293,37424,37529,37771,37938,38142,38350,38621,38733,38845,38950,39067,39281,39427,39567,39653,40001,40089,40335,40753,41002,41084,41182,41774,41874,42126,42550,42805,42899,42988,43225,45249,45491,45593,45846,48002,58534,60050,70681,72209,73966,74592,75012,76073,77338,77594,77830,78377,78871,79476,79674,80254,80818,81193,81311,81849,82006,82202,82475,82731,82901,83042,83106,83471,83838,84514,84778,85116,85469,85563,85749,86055,86317,86442,86569,86808,87019,87138,87331,87508,87963,88144,88266,88525,88638,88825,88927,89034,89163,89438,89946,90442,91319,91613,92183,92332,93064,93236,93320,93656,93748,96082,101328,106717,106779,107357,107941,115888,116001,116230,116390,116542,116713,116879,117048,117215,117378,117621,117791,117964,118135,118409,118608,118813,119143,119227,119323,119419,119517,119617,119719,119821,119923,120025,120127,120227,120323,120435,120564,120687,120818,120949,121047,121161,121255,121395,121529,121625,121737,121837,121953,122049,122161,122261,122401,122537,122701,122831,122989,123139,123280,123424,123559,123671,123821,123949,124077,124213,124345,124475,124605,124717,125997,126143,126287,126425,126491,126581,126657,126761,126851,126953,127061,127169,127269,127349,127441,127539,127649,127727,127833,127925,128029,128139,128261,128424,128991,129071,129171,129261,129371,129461,129702,129796,129902,129994,130094,130206,130320,130436,130552,130646,130760,130872,130974,131094,131216,131298,131402,131522,131648,131746,131840,131928,132040,132156,132278,132390,132565,132681,132767,132859,132971,133095,133162,133288,133356,133484,133628,133756,133825,133920,134035,134148,134247,134356,134467,134578,134679,134784,134884,135014,135105,135228,135322,135434,135520,135624,135720,135808,135926,136030,136134,136260,136348,136456,136556,136646,136756,136840,136942,137026,137080,137144,137250,137336,137446,137530,137934,140550,140668,140783,140863,141224,141810,143214,144558,145919,146307,149082,159171,160211,167024,171325,172076,172338,172870,173249,177527,178381,178610,183218,184228,185763,188163,192287,193031,195162,195502,196813", "endLines": "4,27,28,59,60,61,62,68,69,70,71,72,73,76,77,78,79,80,81,82,83,84,85,90,91,94,95,96,97,98,99,100,101,102,103,104,105,106,107,108,109,110,111,112,113,114,115,120,121,122,123,125,126,127,128,129,131,132,133,134,135,136,137,138,139,140,141,142,143,144,145,146,147,148,149,150,151,152,153,154,155,156,157,158,159,160,161,162,163,164,165,166,167,168,169,170,171,172,173,174,175,176,177,178,179,180,181,182,183,184,185,186,187,188,189,190,191,192,193,194,195,196,197,198,199,200,201,202,203,204,205,206,207,208,209,210,211,212,213,214,215,216,217,227,228,232,233,234,235,236,237,238,268,269,270,271,272,273,274,275,311,312,313,314,319,327,328,333,355,361,362,364,368,369,370,371,372,373,374,375,376,377,378,379,380,381,382,383,384,385,386,387,388,389,390,391,392,393,394,422,427,428,429,430,431,439,440,444,448,452,457,463,470,474,478,483,487,491,495,499,503,507,513,517,523,527,533,537,542,546,549,553,559,563,569,573,579,582,586,590,594,598,602,603,604,605,608,611,614,617,621,622,623,624,625,628,630,632,634,639,640,644,650,654,655,657,668,669,673,679,683,684,685,689,716,720,721,725,753,923,949,1120,1146,1177,1185,1191,1205,1227,1232,1237,1247,1256,1265,1269,1276,1284,1291,1292,1301,1304,1307,1311,1315,1319,1322,1323,1328,1333,1343,1348,1355,1361,1362,1365,1369,1374,1376,1378,1381,1384,1386,1390,1393,1400,1403,1406,1410,1412,1416,1418,1420,1422,1426,1434,1442,1454,1460,1469,1472,1483,1486,1487,1492,1493,1498,1595,1665,1666,1676,1685,1686,1839,1843,1846,1849,1852,1855,1858,1861,1864,1868,1871,1874,1877,1881,1884,1888,1892,1893,1894,1895,1896,1897,1898,1899,1900,1901,1902,1903,1904,1905,1906,1907,1908,1909,1910,1911,1912,1914,1916,1917,1918,1919,1920,1921,1922,1923,1925,1926,1928,1929,1931,1933,1934,1936,1937,1938,1939,1940,1941,1943,1944,1945,1946,1947,1948,1966,1968,1970,1971,1972,1973,1974,1975,1976,1977,1978,1979,1980,1981,1982,1984,1985,1986,1987,1988,1989,1991,1995,1999,2008,2009,2010,2011,2012,2016,2017,2018,2019,2021,2023,2025,2027,2029,2030,2031,2032,2034,2036,2038,2039,2040,2041,2042,2043,2044,2045,2046,2047,2048,2049,2052,2053,2054,2055,2057,2059,2060,2062,2063,2065,2067,2069,2070,2071,2072,2073,2074,2075,2076,2077,2078,2079,2080,2082,2083,2084,2085,2087,2088,2089,2090,2091,2093,2095,2097,2099,2100,2101,2102,2103,2104,2105,2106,2107,2108,2109,2110,2111,2112,2113,2114,2194,2197,2200,2203,2217,2223,2244,2305,2332,2341,2403,2762,2771,2826,2954,3084,3090,3096,3127,3251,3271,3285,3289,3435,3490,3515,3640,3694,3749,3761,3787,3794", "endColumns": "54,44,48,40,54,58,61,80,60,74,75,76,77,84,81,75,75,76,77,105,105,78,79,56,57,73,74,64,65,59,60,71,72,66,67,58,58,58,58,58,53,53,52,53,53,53,53,73,78,72,73,71,71,72,56,57,73,73,74,71,72,69,70,59,60,68,68,69,73,75,63,76,75,76,64,68,76,74,68,67,76,65,60,96,64,68,98,70,58,57,56,58,63,70,71,71,71,71,66,67,67,58,62,63,89,90,59,65,66,65,69,63,52,66,60,66,112,57,62,64,64,74,72,71,48,60,60,60,61,63,63,63,64,62,59,60,65,58,59,61,70,59,67,85,86,89,86,87,81,82,89,90,51,57,44,65,63,56,56,53,56,47,48,50,33,46,48,45,31,63,61,56,73,69,77,53,69,84,47,45,60,62,65,63,70,62,64,63,60,60,51,72,73,68,74,73,73,140,69,52,77,89,87,95,89,12,88,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,136,130,104,12,12,12,12,12,111,111,104,116,12,12,12,12,12,87,12,12,12,81,12,12,99,12,12,12,93,88,12,12,12,101,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,117,12,12,12,12,12,12,12,63,12,12,12,12,12,12,93,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,83,12,91,12,12,12,61,12,12,90,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,83,95,95,97,99,101,101,101,101,101,99,95,111,128,122,130,130,97,113,93,12,12,95,111,99,115,95,111,99,12,135,12,129,12,12,140,12,134,111,149,127,127,12,131,129,129,111,139,12,12,12,65,89,75,103,89,101,107,107,99,79,91,97,12,77,105,91,103,109,12,12,12,79,99,89,109,89,12,93,105,91,12,12,12,12,12,93,113,111,12,12,12,81,103,119,125,97,93,87,111,115,121,111,12,115,85,91,12,12,66,12,67,12,12,12,68,94,114,112,98,108,110,110,100,104,99,12,90,122,93,12,85,103,95,87,12,12,12,12,87,107,99,89,109,83,101,83,53,63,105,85,109,83,119,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24", "endOffsets": "275,931,980,2019,2074,2133,2195,2609,2670,2745,2821,2898,2976,3221,3303,3379,3455,3532,3610,3716,3822,3901,3981,4287,4345,4550,4625,4690,4756,4816,4877,4949,5022,5089,5157,5216,5275,5334,5393,5452,5506,5560,5613,5667,5721,5775,5829,6193,6272,6345,6419,6562,6634,6707,6764,6822,6969,7043,7118,7190,7263,7333,7404,7464,7525,7594,7663,7733,7807,7883,7947,8024,8100,8177,8242,8311,8388,8463,8532,8600,8677,8743,8804,8901,8966,9035,9134,9205,9264,9322,9379,9438,9502,9573,9645,9717,9789,9861,9928,9996,10064,10123,10186,10250,10340,10431,10491,10557,10624,10690,10760,10824,10877,10944,11005,11072,11185,11243,11306,11371,11436,11511,11584,11656,11705,11766,11827,11888,11950,12014,12078,12142,12207,12270,12330,12391,12457,12516,12576,12638,12709,12769,12837,13554,13641,13894,13981,14069,14151,14234,14324,14415,16313,16371,16416,16482,16546,16603,16660,16714,18894,18942,18991,19042,19280,19663,19712,19967,21124,21459,21521,21638,21908,21978,22056,22110,22180,22265,22313,22359,22420,22483,22549,22613,22684,22747,22812,22876,22937,22998,23050,23123,23197,23266,23341,23415,23489,23630,23700,25555,25941,26031,26119,26215,26305,26887,26976,27223,27504,27756,28041,28434,28911,29133,29355,29631,29858,30088,30318,30548,30778,31005,31424,31650,32075,32305,32733,32952,33235,33443,33574,33801,34227,34452,34879,35100,35525,35645,35921,36222,36546,36837,37151,37288,37419,37524,37766,37933,38137,38345,38616,38728,38840,38945,39062,39276,39422,39562,39648,39996,40084,40330,40748,40997,41079,41177,41769,41869,42121,42545,42800,42894,42983,43220,45244,45486,45588,45841,47997,58529,60045,70676,72204,73961,74587,75007,76068,77333,77589,77825,78372,78866,79471,79669,80249,80813,81188,81306,81844,82001,82197,82470,82726,82896,83037,83101,83466,83833,84509,84773,85111,85464,85558,85744,86050,86312,86437,86564,86803,87014,87133,87326,87503,87958,88139,88261,88520,88633,88820,88922,89029,89158,89433,89941,90437,91314,91608,92178,92327,93059,93231,93315,93651,93743,94021,101323,106712,106774,107352,107936,108027,115996,116225,116385,116537,116708,116874,117043,117210,117373,117616,117786,117959,118130,118404,118603,118808,119138,119222,119318,119414,119512,119612,119714,119816,119918,120020,120122,120222,120318,120430,120559,120682,120813,120944,121042,121156,121250,121390,121524,121620,121732,121832,121948,122044,122156,122256,122396,122532,122696,122826,122984,123134,123275,123419,123554,123666,123816,123944,124072,124208,124340,124470,124600,124712,124852,126138,126282,126420,126486,126576,126652,126756,126846,126948,127056,127164,127264,127344,127436,127534,127644,127722,127828,127920,128024,128134,128256,128419,128576,129066,129166,129256,129366,129456,129697,129791,129897,129989,130089,130201,130315,130431,130547,130641,130755,130867,130969,131089,131211,131293,131397,131517,131643,131741,131835,131923,132035,132151,132273,132385,132560,132676,132762,132854,132966,133090,133157,133283,133351,133479,133623,133751,133820,133915,134030,134143,134242,134351,134462,134573,134674,134779,134879,135009,135100,135223,135317,135429,135515,135619,135715,135803,135921,136025,136129,136255,136343,136451,136551,136641,136751,136835,136937,137021,137075,137139,137245,137331,137441,137525,137645,140545,140663,140778,140858,141219,141452,142322,144553,145914,146302,149077,158981,159301,161563,167591,172071,172333,172533,173244,177522,178128,178605,178756,183428,185306,186070,191184,193026,195157,195497,196808,197011"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.10.2\\transforms\\8975063235ea0ba114f3bb0240765838\\transformed\\work-runtime-2.8.1\\res\\values\\values.xml", "from": {"startLines": "2,3,4,5", "startColumns": "4,4,4,4", "startOffsets": "55,120,190,254", "endColumns": "64,69,63,60", "endOffsets": "115,185,249,310"}, "to": {"startLines": "64,65,66,67", "startColumns": "4,4,4,4", "startOffsets": "2273,2338,2408,2472", "endColumns": "64,69,63,60", "endOffsets": "2333,2403,2467,2528"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.10.2\\transforms\\3406eb4cffe3e53bb6d20996e0b4ec87\\transformed\\lifecycle-runtime-2.7.0\\res\\values\\values.xml", "from": {"startLines": "2", "startColumns": "4", "startOffsets": "55", "endColumns": "42", "endOffsets": "93"}, "to": {"startLines": "356", "startColumns": "4", "startOffsets": "21129", "endColumns": "42", "endOffsets": "21167"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.10.2\\transforms\\deb838f5c4813d0c441214e1ddf497cc\\transformed\\core-1.13.1\\res\\values\\values.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60,61,62,63,64,65,66,67,68,69,70,71,72,73,74,75,76,77,78,79,80,81,82,83,84,85,86,87,88,89,90,91,92,93,94,98,99,103,104,105,106,112,122,155,176,209", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "55,115,187,275,340,406,475,538,608,676,748,818,879,953,1026,1087,1148,1210,1274,1336,1397,1465,1565,1625,1691,1764,1833,1890,1942,2004,2076,2152,2217,2276,2335,2395,2455,2515,2575,2635,2695,2755,2815,2875,2935,2994,3054,3114,3174,3234,3294,3354,3414,3474,3534,3594,3653,3713,3773,3832,3891,3950,4009,4068,4127,4162,4197,4252,4315,4370,4428,4486,4547,4610,4667,4718,4768,4829,4886,4952,4986,5021,5056,5126,5193,5265,5334,5403,5477,5549,5637,5708,5825,6026,6136,6337,6466,6538,6605,6808,7109,8840,9521,10203", "endLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60,61,62,63,64,65,66,67,68,69,70,71,72,73,74,75,76,77,78,79,80,81,82,83,84,85,86,87,88,89,90,91,92,93,97,98,102,103,104,105,111,121,154,175,208,214", "endColumns": "59,71,87,64,65,68,62,69,67,71,69,60,73,72,60,60,61,63,61,60,67,99,59,65,72,68,56,51,61,71,75,64,58,58,59,59,59,59,59,59,59,59,59,59,58,59,59,59,59,59,59,59,59,59,59,58,59,59,58,58,58,58,58,58,34,34,54,62,54,57,57,60,62,56,50,49,60,56,65,33,34,34,69,66,71,68,68,73,71,87,70,116,12,109,12,128,71,66,24,24,24,24,24,24", "endOffsets": "110,182,270,335,401,470,533,603,671,743,813,874,948,1021,1082,1143,1205,1269,1331,1392,1460,1560,1620,1686,1759,1828,1885,1937,1999,2071,2147,2212,2271,2330,2390,2450,2510,2570,2630,2690,2750,2810,2870,2930,2989,3049,3109,3169,3229,3289,3349,3409,3469,3529,3589,3648,3708,3768,3827,3886,3945,4004,4063,4122,4157,4192,4247,4310,4365,4423,4481,4542,4605,4662,4713,4763,4824,4881,4947,4981,5016,5051,5121,5188,5260,5329,5398,5472,5544,5632,5703,5820,6021,6131,6332,6461,6533,6600,6803,7104,8835,9516,10198,10365"}, "to": {"startLines": "29,74,75,92,93,116,117,220,221,222,223,224,225,226,242,243,244,245,246,247,248,249,250,251,252,253,254,255,256,276,277,278,279,280,281,282,283,284,285,286,287,288,289,290,291,292,293,294,295,296,297,298,299,300,301,302,303,304,305,306,307,308,309,310,321,322,334,335,336,337,338,339,340,341,342,343,344,345,346,347,348,349,367,396,397,398,399,400,401,402,423,1949,1950,1955,1958,1963,2115,2116,2772,2789,2959,2992,3022,3055", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "985,2981,3053,4350,4415,5834,5903,12985,13055,13123,13195,13265,13326,13400,14643,14704,14765,14827,14891,14953,15014,15082,15182,15242,15308,15381,15450,15507,15559,16719,16791,16867,16932,16991,17050,17110,17170,17230,17290,17350,17410,17470,17530,17590,17650,17709,17769,17829,17889,17949,18009,18069,18129,18189,18249,18309,18368,18428,18488,18547,18606,18665,18724,18783,19351,19386,19972,20027,20090,20145,20203,20261,20322,20385,20442,20493,20543,20604,20661,20727,20761,20796,21769,23788,23855,23927,23996,24065,24139,24211,25560,124857,124974,125241,125534,125801,137650,137722,159306,159910,167745,169476,170476,171158", "endLines": "29,74,75,92,93,116,117,220,221,222,223,224,225,226,242,243,244,245,246,247,248,249,250,251,252,253,254,255,256,276,277,278,279,280,281,282,283,284,285,286,287,288,289,290,291,292,293,294,295,296,297,298,299,300,301,302,303,304,305,306,307,308,309,310,321,322,334,335,336,337,338,339,340,341,342,343,344,345,346,347,348,349,367,396,397,398,399,400,401,402,423,1949,1953,1955,1961,1963,2115,2116,2777,2798,2991,3012,3054,3060", "endColumns": "59,71,87,64,65,68,62,69,67,71,69,60,73,72,60,60,61,63,61,60,67,99,59,65,72,68,56,51,61,71,75,64,58,58,59,59,59,59,59,59,59,59,59,59,58,59,59,59,59,59,59,59,59,59,59,58,59,59,58,58,58,58,58,58,34,34,54,62,54,57,57,60,62,56,50,49,60,56,65,33,34,34,69,66,71,68,68,73,71,87,70,116,12,109,12,128,71,66,24,24,24,24,24,24", "endOffsets": "1040,3048,3136,4410,4476,5898,5961,13050,13118,13190,13260,13321,13395,13468,14699,14760,14822,14886,14948,15009,15077,15177,15237,15303,15376,15445,15502,15554,15616,16786,16862,16927,16986,17045,17105,17165,17225,17285,17345,17405,17465,17525,17585,17645,17704,17764,17824,17884,17944,18004,18064,18124,18184,18244,18304,18363,18423,18483,18542,18601,18660,18719,18778,18837,19381,19416,20022,20085,20140,20198,20256,20317,20380,20437,20488,20538,20599,20656,20722,20756,20791,20826,21834,23850,23922,23991,24060,24134,24206,24294,25626,124969,125170,125346,125730,125925,137717,137784,159504,160206,169471,170152,171153,171320"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.10.2\\transforms\\77d6589d54f70503af1ad7bd5c95eb07\\transformed\\jetified-startup-runtime-1.1.1\\res\\values\\values.xml", "from": {"startLines": "2", "startColumns": "4", "startOffsets": "55", "endColumns": "82", "endOffsets": "133"}, "to": {"startLines": "395", "startColumns": "4", "startOffsets": "23705", "endColumns": "82", "endOffsets": "23783"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.10.2\\transforms\\1ff8cdab1140e9ed76ee2cc1c84e5b27\\transformed\\jetified-appcompat-resources-1.1.0\\res\\values\\values.xml", "from": {"startLines": "2,29,36,47,74", "startColumns": "4,4,4,4,4", "startOffsets": "55,1702,2087,2684,4317", "endLines": "28,35,46,73,78", "endColumns": "24,24,24,24,24", "endOffsets": "1697,2082,2679,4312,4582"}, "to": {"startLines": "2245,2261,2267,3555,3571", "startColumns": "4,4,4,4,4", "startOffsets": "142327,142752,142930,187625,188036", "endLines": "2260,2266,2276,3570,3574", "endColumns": "24,24,24,24,24", "endOffsets": "142747,142925,143209,188031,188158"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.10.2\\transforms\\970764a81dd610c772147726baa7dca5\\transformed\\jetified-window-1.2.0\\res\\values\\values.xml", "from": {"startLines": "2,3,9,17,25,37,43,49,50,51,52,53,54,55,61,66,74,89", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "55,114,287,506,725,1039,1227,1414,1467,1527,1579,1624,1663,1723,1918,2076,2358,2972", "endLines": "2,8,16,24,36,42,48,49,50,51,52,53,54,60,65,73,88,104", "endColumns": "58,11,11,11,11,11,11,52,59,51,44,38,59,24,24,24,24,24", "endOffsets": "109,282,501,720,1034,1222,1409,1462,1522,1574,1619,1658,1718,1913,2071,2353,2967,3621"}, "to": {"startLines": "2,5,11,19,30,42,48,54,55,56,57,58,315,2224,2230,3516,3524,3539", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "105,280,453,672,1045,1359,1547,1734,1787,1847,1899,1944,19047,141457,141652,186075,186357,186971", "endLines": "2,10,18,26,41,47,53,54,55,56,57,58,315,2229,2234,3523,3538,3554", "endColumns": "58,11,11,11,11,11,11,52,59,51,44,38,59,24,24,24,24,24", "endOffsets": "159,448,667,886,1354,1542,1729,1782,1842,1894,1939,1978,19102,141647,141805,186352,186966,187620"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.10.2\\transforms\\f3900c31e436e087be46264d65f1dea9\\transformed\\jetified-media3-exoplayer-1.4.1\\res\\values\\values.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10", "startColumns": "4,4,4,4,4,4,4,4,4", "startOffsets": "55,125,187,252,316,393,458,548,632", "endColumns": "69,61,64,63,76,64,89,83,68", "endOffsets": "120,182,247,311,388,453,543,627,696"}, "to": {"startLines": "405,406,407,408,409,410,411,412,413", "startColumns": "4,4,4,4,4,4,4,4,4", "startOffsets": "24405,24475,24537,24602,24666,24743,24808,24898,24982", "endColumns": "69,61,64,63,76,64,89,83,68", "endOffsets": "24470,24532,24597,24661,24738,24803,24893,24977,25046"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.10.2\\transforms\\9306062905f4d3cda1670c0e5f3df17e\\transformed\\media-1.1.0\\res\\values\\values.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "55,144,215,288,341,394,447,500,560,626,748,809,875", "endColumns": "88,70,72,52,52,52,52,59,65,121,60,65,66", "endOffsets": "139,210,283,336,389,442,495,555,621,743,804,870,937"}, "to": {"startLines": "118,124,130,264,265,266,267,363,1954,1956,1957,1962,1964", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "5966,6424,6827,16054,16107,16160,16213,21526,125175,125351,125473,125735,125930", "endColumns": "88,70,72,52,52,52,52,59,65,121,60,65,66", "endOffsets": "6050,6490,6895,16102,16155,16208,16261,21581,125236,125468,125529,125796,125992"}}, {"source": "D:\\PROGRAMMING\\AC-FIT\\acfit_app\\android\\app\\src\\main\\res\\values\\styles.xml", "from": {"startLines": "3,17", "startColumns": "4,4", "startOffsets": "173,1023", "endLines": "10,19", "endColumns": "12,12", "endOffsets": "681,1187"}, "to": {"startLines": "1516,1524", "startColumns": "4,4", "startOffsets": "95464,95913", "endLines": "1523,1526", "endColumns": "12,12", "endOffsets": "95908,96077"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.10.2\\transforms\\812b0debb255957f3fcc9a2669f27c22\\transformed\\fragment-1.7.1\\res\\values\\values.xml", "from": {"startLines": "2,3,4,5,10", "startColumns": "4,4,4,4,4", "startOffsets": "55,112,177,241,411", "endLines": "2,3,4,9,13", "endColumns": "56,64,63,24,24", "endOffsets": "107,172,236,406,555"}, "to": {"startLines": "316,332,360,3013,3018", "startColumns": "4,4,4,4,4", "startOffsets": "19107,19861,21336,170157,170327", "endLines": "316,332,360,3017,3021", "endColumns": "56,64,63,24,24", "endOffsets": "19159,19921,21395,170322,170471"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.10.2\\transforms\\3e9c3e49153aa47f3a49791ab647f926\\transformed\\transition-1.4.1\\res\\values\\values.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11", "startColumns": "4,4,4,4,4,4,4,4,4,4", "startOffsets": "55,95,142,185,240,287,341,393,442,503", "endColumns": "39,46,42,54,46,53,51,48,60,49", "endOffsets": "90,137,180,235,282,336,388,437,498,548"}, "to": {"startLines": "317,318,323,330,331,350,351,352,353,354", "startColumns": "4,4,4,4,4,4,4,4,4,4", "startOffsets": "19164,19204,19421,19759,19814,20831,20885,20937,20986,21047", "endColumns": "39,46,42,54,46,53,51,48,60,49", "endOffsets": "19199,19246,19459,19809,19856,20880,20932,20981,21042,21092"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.10.2\\transforms\\b6435e177046c519b4e9a09d53555b3f\\transformed\\coordinatorlayout-1.0.0\\res\\values\\values.xml", "from": {"startLines": "2,102,3,13", "startColumns": "4,4,4,4", "startOffsets": "55,5935,116,724", "endLines": "2,104,12,101", "endColumns": "60,12,24,24", "endOffsets": "111,6075,719,5930"}, "to": {"startLines": "3,2117,2827,2833", "startColumns": "4,4,4,4", "startOffsets": "164,137789,161568,161779", "endLines": "3,2119,2832,2916", "endColumns": "60,12,24,24", "endOffsets": "220,137929,161774,166290"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.10.2\\transforms\\594b8e088512a24631e5a37668efcdba\\transformed\\lifecycle-viewmodel-2.7.0\\res\\values\\values.xml", "from": {"startLines": "2", "startColumns": "4", "startOffsets": "55", "endColumns": "49", "endOffsets": "100"}, "to": {"startLines": "359", "startColumns": "4", "startOffsets": "21286", "endColumns": "49", "endOffsets": "21331"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.10.2\\transforms\\ae5f57bd372fc0b34c3bf33092f8edd1\\transformed\\jetified-savedstate-1.2.1\\res\\values\\values.xml", "from": {"startLines": "2", "startColumns": "4", "startOffsets": "55", "endColumns": "53", "endOffsets": "104"}, "to": {"startLines": "358", "startColumns": "4", "startOffsets": "21232", "endColumns": "53", "endOffsets": "21281"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.10.2\\transforms\\0aea3dee6044b352a54f8fb9c4a12c07\\transformed\\preference-1.2.1\\res\\values\\values.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,22,23,24,25,42,45,51,57,60,66,70,73,80,86,89,95,100,105,112,114,120,126,134,139,146,151,157,161,168,172,178,184,187,192,193,194,199,215,238,243,257,268,348,358,368,386,392,439,461,485", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "105,178,247,311,366,434,501,566,623,680,728,776,837,900,963,1001,1058,1102,1242,1381,1431,1479,2917,3022,3378,3716,3862,4202,4414,4577,4984,5322,5445,5784,6023,6280,6651,6711,7049,7335,7784,8076,8464,8769,9113,9358,9688,9895,10163,10436,10580,10949,10996,11052,11308,12367,13788,14126,15012,15622,20168,20687,21229,22503,22763,25467,26989,28470", "endLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,21,22,23,24,41,44,50,56,59,65,69,72,79,85,88,94,99,104,111,113,119,125,133,138,145,150,156,160,167,171,177,183,186,191,192,193,198,214,237,242,256,267,347,357,367,385,391,438,460,484,508", "endColumns": "72,68,63,54,67,66,64,56,56,47,47,60,62,62,37,56,43,13,138,49,47,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,46,55,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24", "endOffsets": "173,242,306,361,429,496,561,618,675,723,771,832,895,958,996,1053,1097,1237,1376,1426,1474,2912,3017,3373,3711,3857,4197,4409,4572,4979,5317,5440,5779,6018,6275,6646,6706,7044,7330,7779,8071,8459,8764,9108,9353,9683,9890,10158,10431,10575,10944,10991,11047,11303,12362,13783,14121,15007,15617,20163,20682,21224,22498,22758,25462,26984,28465,29984"}, "to": {"startLines": "63,119,257,258,259,260,261,262,263,324,325,326,365,366,403,414,418,419,424,425,426,1499,1687,1690,1696,1702,1705,1711,1715,1718,1725,1731,1734,1740,1745,1750,1757,1759,1765,1771,1779,1784,1791,1796,1802,1806,1813,1817,1823,1829,1832,1836,1837,2763,2778,2917,2955,3097,3272,3290,3354,3364,3374,3381,3387,3491,3641,3658", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "2200,6055,15621,15685,15740,15808,15875,15940,15997,19464,19512,19560,21643,21706,24299,25051,25323,25367,25631,25770,25820,94026,108032,108137,108382,108720,108866,109206,109418,109581,109988,110326,110449,110788,111027,111284,111655,111715,112053,112339,112788,113080,113468,113773,114117,114362,114692,114899,115167,115440,115584,115785,115832,158986,159509,166295,167596,172538,178133,178761,180686,180968,181273,181535,181795,185311,191189,191719", "endLines": "63,119,257,258,259,260,261,262,263,324,325,326,365,366,403,414,418,421,424,425,426,1515,1689,1695,1701,1704,1710,1714,1717,1724,1730,1733,1739,1744,1749,1756,1758,1764,1770,1778,1783,1790,1795,1801,1805,1812,1816,1822,1828,1831,1835,1836,1837,2767,2788,2936,2958,3106,3279,3353,3363,3373,3380,3386,3429,3503,3657,3674", "endColumns": "72,68,63,54,67,66,64,56,56,47,47,60,62,62,37,56,43,13,138,49,47,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,46,55,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24", "endOffsets": "2268,6119,15680,15735,15803,15870,15935,15992,16049,19507,19555,19616,21701,21764,24332,25103,25362,25502,25765,25815,25863,95459,108132,108377,108715,108861,109201,109413,109576,109983,110321,110444,110783,111022,111279,111650,111710,112048,112334,112783,113075,113463,113768,114112,114357,114687,114894,115162,115435,115579,115780,115827,115883,159166,159905,167019,167740,172865,178376,180681,180963,181268,181530,181790,183213,185758,191714,192282"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.10.2\\transforms\\5683f3a75f4aae74864d56d25d09ab49\\transformed\\browser-1.8.0\\res\\values\\values.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11", "startColumns": "4,4,4,4,4,4,4,4,4,4", "startOffsets": "55,113,179,242,304,375,447,515,582,661", "endColumns": "57,65,62,61,70,71,67,66,78,68", "endOffsets": "108,174,237,299,370,442,510,577,656,725"}, "to": {"startLines": "86,87,88,89,218,219,404,415,416,417", "startColumns": "4,4,4,4,4,4,4,4,4,4", "startOffsets": "3986,4044,4110,4173,12842,12913,24337,25108,25175,25254", "endColumns": "57,65,62,61,70,71,67,66,78,68", "endOffsets": "4039,4105,4168,4230,12908,12980,24400,25170,25249,25318"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.10.2\\transforms\\e57d2fe08522bc82b0ac6f830a2f178d\\transformed\\jetified-activity-1.9.3\\res\\values\\values.xml", "from": {"startLines": "2,3", "startColumns": "4,4", "startOffsets": "55,97", "endColumns": "41,59", "endOffsets": "92,152"}, "to": {"startLines": "329,357", "startColumns": "4,4", "startOffsets": "19717,21172", "endColumns": "41,59", "endOffsets": "19754,21227"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.10.2\\transforms\\b2df645d79e711076ea45b6a8385e99c\\transformed\\recyclerview-1.0.0\\res\\values\\values.xml", "from": {"startLines": "30,31,32,33,34,35,36,2", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "1535,1594,1642,1698,1773,1849,1921,55", "endLines": "30,31,32,33,34,35,36,29", "endColumns": "58,47,55,74,75,71,65,24", "endOffsets": "1589,1637,1693,1768,1844,1916,1982,1530"}, "to": {"startLines": "229,230,231,239,240,241,320,3436", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "13646,13705,13753,14420,14495,14571,19285,183433", "endLines": "229,230,231,239,240,241,320,3455", "endColumns": "58,47,55,74,75,71,65,24", "endOffsets": "13700,13748,13804,14490,14566,14638,19346,184223"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.10.2\\transforms\\9b10a4e78f0e7750b24f9632f5e597a2\\transformed\\jetified-core-1.10.3\\res\\values\\values.xml", "from": {"startLines": "4", "startColumns": "0", "startOffsets": "166", "endLines": "11", "endColumns": "8", "endOffsets": "571"}, "to": {"startLines": "2000", "startColumns": "4", "startOffsets": "128581", "endLines": "2007", "endColumns": "8", "endOffsets": "128986"}}]}]}