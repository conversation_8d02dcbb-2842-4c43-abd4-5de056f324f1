<variant
    name="release"
    package="dev.fluttercommunity.plus.connectivity"
    minSdkVersion="19"
    targetSdkVersion="19"
    mergedManifest="D:\PROGRAMMING\AC-FIT\acfit_app\build\connectivity_plus\intermediates\merged_manifest\release\processReleaseManifest\AndroidManifest.xml"
    manifestMergeReport="D:\PROGRAMMING\AC-FIT\acfit_app\build\connectivity_plus\outputs\logs\manifest-merger-release-report.txt"
    proguardFiles="D:\PROGRAMMING\AC-FIT\acfit_app\build\connectivity_plus\intermediates\default_proguard_files\global\proguard-android.txt-8.7.0"
    partialResultsDir="D:\PROGRAMMING\AC-FIT\acfit_app\build\connectivity_plus\intermediates\lint_partial_results\release\lintAnalyzeRelease\out">
  <buildFeatures
      namespacing="REQUIRED"/>
  <sourceProviders>
    <sourceProvider
        manifests="src\main\AndroidManifest.xml"
        javaDirectories="src\main\java;src\release\java;src\main\kotlin;src\release\kotlin"
        resDirectories="src\main\res;src\release\res"
        assetsDirectories="src\main\assets;src\release\assets"/>
  </sourceProviders>
  <testSourceProviders>
  </testSourceProviders>
  <testFixturesSourceProviders>
  </testFixturesSourceProviders>
  <artifact
      classOutputs="D:\PROGRAMMING\AC-FIT\acfit_app\build\connectivity_plus\intermediates\javac\release\compileReleaseJavaWithJavac\classes;D:\PROGRAMMING\AC-FIT\acfit_app\build\connectivity_plus\intermediates\compile_r_class_jar\release\generateReleaseRFile\R.jar"
      type="MAIN"
      applicationId="dev.fluttercommunity.plus.connectivity"
      generatedSourceFolders="D:\PROGRAMMING\AC-FIT\acfit_app\build\connectivity_plus\generated\ap_generated_sources\release\out;D:\PROGRAMMING\AC-FIT\acfit_app\build\connectivity_plus\generated\source\buildConfig\release"
      generatedResourceFolders="D:\PROGRAMMING\AC-FIT\acfit_app\build\connectivity_plus\generated\res\resValues\release"
      desugaredMethodsFiles="C:\Users\<USER>\.gradle\caches\8.10.2\transforms\917193aa7db7ac16424c571c132278d7\transformed\D8BackportedDesugaredMethods.txt">
  </artifact>
</variant>
